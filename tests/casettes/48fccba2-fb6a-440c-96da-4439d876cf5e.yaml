interactions:
- request:
    body: '{"max_tokens": 1024, "messages": [{"role": "user", "content": "You are
      doing web research on behalf of a user. You are trying to figure out this information:\n\n<info>\n{\n  \"type\":
      \"object\",\n  \"properties\": {\n    \"founder\": {\n      \"type\": \"string\",\n      \"description\":
      \"The name of the company founder.\"\n    },\n    \"websiteUrl\": {\n      \"type\":
      \"string\",\n      \"description\": \"Website URL of the company, e.g.: https://openai.com/,
      or https://microsoft.com\"\n    },\n    \"products_sold\": {\n      \"type\":
      \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      },\n      \"description\":
      \"A list of products sold by the company.\"\n    }\n  },\n  \"required\": [\n    \"founder\",\n    \"websiteUrl\",\n    \"products_sold\"\n  ]\n}\n</info>\n\nYou
      have access to the following tools:\n\n- `Search`: call a search tool and get
      back some results\n- `ScrapeWebsite`: scrape a website and get relevant notes
      about the given request. This will update the notes above.\n- `Info`: call this
      when you are done and have gathered all the relevant info\n\nHere is the information
      you have about the topic you are researching:\n\nTopic: LangChain"}], "model":
      "claude-3-5-sonnet-20240620", "tools": [{"name": "scrape_website", "description":
      "Scrape and summarize content from a given URL.", "input_schema": {"type": "object",
      "properties": {"url": {"type": "string"}}, "required": ["url"]}}, {"name": "search",
      "description": "Search for general results. This function performs a search
      using the Tavily search engine, which is designed\nto provide comprehensive,
      accurate, and trusted results. It''s particularly useful\nfor answering questions
      about current events.", "input_schema": {"type": "object", "properties": {"query":
      {"type": "string"}}, "required": ["query"]}}, {"name": "Info", "description":
      "Call this when you have gathered all the relevant info", "input_schema": {"type":
      "object", "properties": {"founder": {"type": "string", "description": "The name
      of the company founder."}, "websiteUrl": {"type": "string", "description": "Website
      URL of the company, e.g.: https://openai.com/, or https://microsoft.com"}, "products_sold":
      {"type": "array", "items": {"type": "string"}, "description": "A list of products
      sold by the company."}}, "required": ["founder", "websiteUrl", "products_sold"]}}]}'
    headers: {}
    method: POST
    uri: https://api.anthropic.com/v1/messages
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA1SRQW8aQQyF/4rrSy5DtBBaKXtLOaRtiBSqUqmtKmR2zTJisJcZD4Qi/nu1G5Gm
        p9HY857f5zmhr7HEbWoWxfDj6H722C7nm+P9w7wa76rJbPPNo0M7tty94pSoYXQYNXQFSsknIzF0
        uNWaA5ZYBco1D24G7wdJRdgGo2I0Lj6MCnRYqRiLYfnrdDE1fu7k/VHihKORl3B8B5+vQoCcGGzN
        QHvygZaBwVRDAlNoyNYcwctK45bMqwAtNRtMSZrJmrw4WGmVk5cGVHqblWapOTo48DJ5Y5h/nTog
        qaGNWufKEiQN9TVM2a4SJKNocPC2BoKGhSMFSEyxWvcJ2IAEdM9x7/lwjWf3D0s1LHLqltVvuLvn
        RTGc39KXp5/T+cP3P7OnH8+Pn8arm9EdOhTadroX904lbTYsT7jLHI9Y4isWVLptSY4XmleYCwOe
        z78dJtN2EZmSyv95+kbiXWapGEvJITjM/c+Wp5e5C9MNS8Lythg71Gxva8OiOJ//AgAA//8DAME3
        o+E5AgAA
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c40d127ddb81592-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 16 Sep 2024 12:21:36 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Robots-Tag:
      - none
      anthropic-ratelimit-requests-limit:
      - '4000'
      anthropic-ratelimit-requests-remaining:
      - '3999'
      anthropic-ratelimit-requests-reset:
      - '2024-09-16T12:21:54Z'
      anthropic-ratelimit-tokens-limit:
      - '400000'
      anthropic-ratelimit-tokens-remaining:
      - '400000'
      anthropic-ratelimit-tokens-reset:
      - '2024-09-16T12:21:36Z'
      request-id:
      - req_01BpGgk7P4Kyk4nyPKuU67fa
      via:
      - 1.1 google
      x-cloud-trace-context:
      - d69ad6e9aaadd4ddb3df60a2193eec5e
    status:
      code: 200
      message: OK
- request:
    body: null
    headers: {}
    method: POST
    uri: https://api.tavily.com/search
  response:
    body:
      string: "{\"query\":\"LangChain company founder website products\",\"follow_up_questions\":null,\"answer\":null,\"images\":[],\"results\":[{\"title\":\"LangChain
        - Products, Competitors, Financials, Employees, Headquarters ...\",\"url\":\"https://www.cbinsights.com/company/langchain\",\"content\":\"It
        was founded in 2023 and is based in San Francisco, California. LangChain specializes
        in the development of large language model (LLM) applications, providing a
        suite of products that support developers throughout the application lifecycle.
        Use the CB Insights Platform to explore LangChain's full profile.\",\"score\":0.98942953,\"raw_content\":null},{\"title\":\"LangChain
        - Wikipedia\",\"url\":\"https://en.wikipedia.org/wiki/LangChain\",\"content\":\"In
        October 2023 LangChain introduced LangServe, a deployment tool designed to
        facilitate the transition from LCEL (LangChain Expression Language) prototypes
        to production-ready applications.[5]\\nIntegrations[edit]\\nAs of March 2023,
        LangChain included integrations with systems including Amazon, Google, and
        Microsoft Azure cloud storage; API wrappers for news, movie information, and
        weather; Bash for summarization, syntax and semantics checking, and execution
        of shell scripts; multiple web scraping subsystems and templates; few-shot
        learning prompt generation support; finding and summarizing \\\"todo\\\" tasks
        in code; Google Drive documents, spreadsheets, and presentations summarization,
        extraction, and creation; Google Search and Microsoft Bing web search; OpenAI,
        Anthropic, and Hugging Face language models; iFixit repair guides and wikis
        search and summarization; MapReduce for question answering, combining documents,
        and question generation; N-gram overlap scoring; PyPDF, pdfminer, fitz, and
        pymupdf for PDF file text extraction and manipulation; Python and JavaScript
        code generation, analysis, and debugging; Milvus vector database[6] to store
        and retrieve vector embeddings; Weaviate vector database[7] to cache embedding
        and data objects; Redis cache database storage; Python RequestsWrapper and
        other methods for API requests; SQL and NoSQL databases including JSON support;
        Streamlit, including for logging; text mapping for k-nearest neighbors search;
        time zone conversion and calendar operations; tracing and recording stack
        symbols in threaded and asynchronous subprocess runs; and the Wolfram Alpha
        website and SDK.[8] As a language model integration framework, LangChain's
        use-cases largely overlap with those of language models in general, including
        document analysis and summarization, chatbots, and code analysis.[2]\\nHistory[edit]\\nLangChain
        was launched in October 2022 as an open source project by Harrison Chase,
        while working at machine learning startup Robust Intelligence. In April 2023,
        LangChain had incorporated and the new startup raised over $20 million in
        funding at a valuation of at least $200 million from venture firm Sequoia
        Capital, a week after announcing a $10 million seed investment from Benchmark.[3][4]\\n
        The project quickly garnered popularity, with improvements from hundreds of
        contributors on GitHub, trending discussions on Twitter, lively activity on
        the project's Discord server, many YouTube tutorials, and meetups in San Francisco
        and London. As of April 2023, it can read from more than 50 document types
        and data sources.[9]\\nReferences[edit]\\nExternal links[edit]\",\"score\":0.98631054,\"raw_content\":null},{\"title\":\"LangChain
        | Company Overview & News - Forbes\",\"url\":\"https://www.forbes.com/companies/langchain/\",\"content\":\"LangChain.
        Founded in late 2022, LangChain was an instant hit in Silicon Valley for its
        software that enables anyone to build apps powered by large language models
        like GPT-4 in as little as two ...\",\"score\":0.9856077,\"raw_content\":null},{\"title\":\"Harrison
        Chase | TEDAI San Francisco\",\"url\":\"https://tedai-sanfrancisco.ted.com/speakers-1/harrison-chase-/co-founder-and-ceo,-langchain/\",\"content\":\"Harrison
        Chase, a Harvard graduate in statistics and computer science, co-founded LangChain
        to streamline the development of Language Model applications with open-source
        Python/Typescript packages. Chase's experience includes heading the Machine
        Learning team at Robust Intelligence, focusing on the testing and validation
        of machine learning models, and leading the entity linking team at Kensho
        ...\",\"score\":0.97680235,\"raw_content\":null},{\"title\":\"LangChain -
        Crunchbase Company Profile & Funding\",\"url\":\"https://www.crunchbase.com/organization/langchain\",\"content\":\"Summary.
        LangChain may be growing as evidenced by securing $25 million in funding,
        which is a significant financial milestone for the company. Additionally,
        the launch of a new platform to facilitate the full lifecycle of Large Language
        Model applications suggests expansion in their product offerings. The integration
        of Gemini Pro API, enabling ...\",\"score\":0.896614,\"raw_content\":null},{\"title\":\"LangChain
        Company Profile - Office Locations, Competitors, Revenue ...\",\"url\":\"https://craft.co/langchain\",\"content\":\"LangChain
        has 5 employees at their 1 location and $35 m in total funding,. See insights
        on LangChain including office locations, competitors, revenue, financials,
        executives, subsidiaries and more at Craft. ... Harrison Chase, Co-Founder
        and Chief Executive Officer. Linkedin;\",\"score\":0.8738621,\"raw_content\":null},{\"title\":\"About
        - LangChain\",\"url\":\"https://www.langchain.com/about\",\"content\":\"Ready
        to start shipping\\nreliable GenAI apps faster?\\nLangChain and LangSmith
        are critical parts of the reference\\narchitecture to get you from prototype
        to production. Announcing the General Availability of LangSmith\\nToday, we\u2019re
        thrilled to announce the general availability of LangSmith \u2014 our solution
        for LLM application development, monitoring, and testing.\\n We build products
        that enable developers to go from an idea to working code in an afternoon
        and in the hands of users in days or weeks. The biggest developer community
        in \_GenAI\\nLearn alongside the 100K+ practitioners\\nwho are pushing the
        industry forward.\\n And we built LangSmith to support all stages of the AI
        engineering lifecycle, to get applications into production faster.\\n\",\"score\":0.8677098,\"raw_content\":null},{\"title\":\"What
        is LangChain: How It Enables Businesses to Do More with LLMs\",\"url\":\"https://www.bluelabellabs.com/blog/what-is-langchain/\",\"content\":\"Similar
        setups to AutoTweeter would allow businesses to parse past and present customer
        queries to understand drop-off points and build the appropriate flows to solve
        customer problems on the spot, instead of spitting them into a queue to wait
        for a human and plot how they\u2019re going to steal from your business.\\n
        Rather than creating a bot to tweet in your tone on your behalf about your
        love for processed snacks, hockey, and political happenings, you could train
        such a system on your brand voice, knowledge bases, and other resources to
        create a highly valuable feature for your digital touchpoints.\\n Here, you
        essentially create a curated ML system that\u2019s tailored to your brand
        with specific issues and requests from your customers \u2013 with a little
        bit of dev magic, you can quickly build a solution that integrates features
        like payment systems, tech support, ordering, account management, and so much
        more.\\n Creating a bot for social media that can post just like you\\nSocial
        media already has enough bots engaging with people as if they\u2019re real
        people, but for the sake of making a functional example, our co-founder Bobby
        created a bot called AutoTweeter for the social media platform we all still
        call Twitter.\\n How does LangChain work?\\nLangchain essentially serves as
        a middleman between other LLMs, data sources, and API-accessible functions
        \u2013 more precisely, it\u2019s a comprehensive open-source library of components
        that allow users to build better (i.e., more custom) solutions by interconnecting
        different resources.\\n\",\"score\":0.8473372,\"raw_content\":null},{\"title\":\"The
        Point of LangChain \u2014 with Harrison Chase of LangChain\",\"url\":\"https://www.latent.space/p/langchain\",\"content\":\"And
        so you can do a lot of things like reflection and kind of like making sure
        that you're on the right track and really put more safeguards and thinking
        about these agents as opposed to relying on like chas and interface, like
        the bot we have that's on GitHub answering questions on the issues, I think
        probably gives better answers than the bots that we have that are on chat
        on the website. [00:50:32]\\nHarrison: And then between kind of like allowing
        for, I think one helpful thing that we did in terms of like distinguishing
        between basically the base interfaces and then more complex stuff is part
        of the separation around the docs is there's like the components piece, which
        has the model IO, the retrieval, the agents, the callbacks, things like that.
        And then like, I know a lot of people are doing this, but like a personal
        assistant for like managing like email calendar, basic stuff, which I think
        is, I think that's like a fantastic application for these like agent like
        things, because if you think about personal assistants today, you usually
        interact, I don't have one, but I'm told you interact with them over email.
        Show Notes\\nLangChain\\nLangChain\u2019s Berkshire Hathaway Homepage\\nAbstractions
        tweet\\nLangSmith\\nLangSmith Cookbooks repo\\nLangChain Retrieval blog\\nEvaluating
        CSV Question/Answering blog and YouTube\\nMultiOn Partner blog\\nHarvard Sports
        Analytics Collective\\nEvaluating RAG Webinar\\nawesome-langchain:\\nLLM Math
        Chain\\nSelf-Ask\\nLangChain Hub UI\\n\u201CLangChain is Pointless\u201D\\nHarrison\u2019s
        links\\nsports - estimating player compatibility in the NBA\\nearly interest
        in prompt injections\\nGitHub\\nTwitter\\nTimestamps\\n[00:00:00] Introduction\\n[00:00:48]
        Harrison's background and how sports led him into ML\\n[00:04:54] Memory (for
        long running chats, whether a simple Buffer, Knowledge Graph, Summary, or
        Vector Store)\\nUse-Cases: compositions of Components\\nChains: combining
        a PromptTemplate, LLM Model and optional OutputParser\\nwith Router, Sequential,
        and Transform Chains for advanced usecases\\nsavable, sharable schemas that
        can be loaded from LangChainHub\\nAgents: a chain that has access to a suite
        of tools, of nondeterministic8 length because the LLM is used as a reasoning
        engine to determine which actions to take and in which order.\",\"score\":0.79549927,\"raw_content\":null},{\"title\":\"LangChain\",\"url\":\"https://www.langchain.com/\",\"content\":\"Join
        Discord\\nContribute on GitHub\\n5.3M\\nMonthly Downloads\\n67k\\nGithub Stars\\n1800+\\nContributors\\n34k+\\nApps
        Powered\\nReady to build?\\nGo To Docs\\nGo To Docs\\nGo To Docs\\nContact
        Sales\\nContact Sales\\nContact Sales\\nProducts and use-cases\\nLangChain\\nLangSmith\\nRetrieval\\nAgents\\nInspiration\\nCode\\nGitHub\\nLangChain
        Hub\\nPython Docs\\nJS/TS Docs\\nSocial\\nTwitter\\nDiscord\\nBlog\\nLinkedIn\\nYouTube\\nTerms
        of Service\\nSign up for our newsletter\\nProducts and use-cases\\nLangChain\\nLangSmith\\nRetrieval\\nAgents\\nInspiration\\nCode\\nGitHub\\nLangChain
        Hub\\nPython Docs\\nJS/TS Docs\\nSocial\\nTwitter\\nDiscord\\nBlog\\nLinkedIn\\nYouTube\\nTerms
        of Service\\nSign up for our newsletter\\nProducts and use-cases\\nLangChain\\nLangSmith\\nRetrieval\\nAgents\\nInspiration\\nCode\\nGitHub\\nLangChain
        Hub\\nPython Docs\\nJS/TS Docs\\nSocial\\nTwitter\\nDiscord\\nBlog\\nLinkedIn\\nYouTube\\nTerms
        of Service\\nSign up for our newsletter Chatbots\\nQ&A Over Docs\\nSummarization\\nCopilots\\nWorkflow
        Automation\\nDocument Analysis\\nCustom Search\\nHarness the power\u2013and
        wrangle the complexity\u2013 of LLMs\\nGet from proof-of-concept to production
        on one platform\\nRely on a set of tools built especially for non-deterministic
        models\\nLeverage new cognitive architectures and battle-tested orchestration\\nThanks
        to our contributors and partners for making LangChain awesome\\nWhen you build
        with LangChain, you build alongside a community of developers advancing the
        frontier of what\u2019s possible with LLMs.\\n \U0001F99C\U0001F517 LangChain\\nLangSmith\\nLangServe\\nAgents\\nRetrieval\\nEvaluation\\nBlog\\nDocs\\n\U0001F99C\U0001F517
        LangChain\\n\U0001F99C\U0001F517 LangChain\\nLangChain Expression Language
        makes it easy to create custom chains.\\n Go To Docs\\nGo To Docs\\nGo To
        Docs\\nContact Sales\\nglobal corporations, STARTUPS, and TINKERERS build
        with LangChain\\nglobal corporations, STARTUPS, and TINKERERS build with LangChain\\nglobal
        corporations, STARTUPS, and TINKERERS build with LangChain\\nA complete set
        of powerful building blocks\\nA complete set of powerful building blocks\\n
        Try it out here\\nGet your LLM application from prototype to production\\nGet
        your LLM application from prototype to production\\nBuild context-aware, reasoning
        applications with LangChain\u2019s flexible abstractions and AI-first toolkit.\\n\",\"score\":0.78613067,\"raw_content\":null}],\"response_time\":3.06}"
    headers:
      Connection:
      - keep-alive
      Content-Length:
      - '12727'
      Content-Type:
      - application/json
      Date:
      - Mon, 16 Sep 2024 12:21:39 GMT
      Server:
      - nginx
    status:
      code: 200
      message: OK
- request:
    body: '{"max_tokens": 1024, "messages": [{"role": "user", "content": "You are
      doing web research on behalf of a user. You are trying to figure out this information:\n\n<info>\n{\n  \"type\":
      \"object\",\n  \"properties\": {\n    \"founder\": {\n      \"type\": \"string\",\n      \"description\":
      \"The name of the company founder.\"\n    },\n    \"websiteUrl\": {\n      \"type\":
      \"string\",\n      \"description\": \"Website URL of the company, e.g.: https://openai.com/,
      or https://microsoft.com\"\n    },\n    \"products_sold\": {\n      \"type\":
      \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      },\n      \"description\":
      \"A list of products sold by the company.\"\n    }\n  },\n  \"required\": [\n    \"founder\",\n    \"websiteUrl\",\n    \"products_sold\"\n  ]\n}\n</info>\n\nYou
      have access to the following tools:\n\n- `Search`: call a search tool and get
      back some results\n- `ScrapeWebsite`: scrape a website and get relevant notes
      about the given request. This will update the notes above.\n- `Info`: call this
      when you are done and have gathered all the relevant info\n\nHere is the information
      you have about the topic you are researching:\n\nTopic: LangChain"}, {"role":
      "assistant", "content": [{"text": "Certainly! I''ll use the available tools
      to gather information about LangChain, focusing on the founder, website URL,
      and products sold. Let''s start with a general search to get an overview.",
      "type": "text"}, {"type": "tool_use", "name": "search", "input": {"query": "LangChain
      company founder website products"}, "id": "toolu_01U9aJPZLUKVzQPYxMH4f32A"}]},
      {"role": "user", "content": [{"type": "tool_result", "content": "[{\"url\":
      \"https://www.cbinsights.com/company/langchain\", \"content\": \"It was founded
      in 2023 and is based in San Francisco, California. LangChain specializes in
      the development of large language model (LLM) applications, providing a suite
      of products that support developers throughout the application lifecycle. Use
      the CB Insights Platform to explore LangChain''s full profile.\"}, {\"url\":
      \"https://en.wikipedia.org/wiki/LangChain\", \"content\": \"In October 2023
      LangChain introduced LangServe, a deployment tool designed to facilitate the
      transition from LCEL (LangChain Expression Language) prototypes to production-ready
      applications.[5]\\nIntegrations[edit]\\nAs of March 2023, LangChain included
      integrations with systems including Amazon, Google, and Microsoft Azure cloud
      storage; API wrappers for news, movie information, and weather; Bash for summarization,
      syntax and semantics checking, and execution of shell scripts; multiple web
      scraping subsystems and templates; few-shot learning prompt generation support;
      finding and summarizing \\\"todo\\\" tasks in code; Google Drive documents,
      spreadsheets, and presentations summarization, extraction, and creation; Google
      Search and Microsoft Bing web search; OpenAI, Anthropic, and Hugging Face language
      models; iFixit repair guides and wikis search and summarization; MapReduce for
      question answering, combining documents, and question generation; N-gram overlap
      scoring; PyPDF, pdfminer, fitz, and pymupdf for PDF file text extraction and
      manipulation; Python and JavaScript code generation, analysis, and debugging;
      Milvus vector database[6] to store and retrieve vector embeddings; Weaviate
      vector database[7] to cache embedding and data objects; Redis cache database
      storage; Python RequestsWrapper and other methods for API requests; SQL and
      NoSQL databases including JSON support; Streamlit, including for logging; text
      mapping for k-nearest neighbors search; time zone conversion and calendar operations;
      tracing and recording stack symbols in threaded and asynchronous subprocess
      runs; and the Wolfram Alpha website and SDK.[8] As a language model integration
      framework, LangChain''s use-cases largely overlap with those of language models
      in general, including document analysis and summarization, chatbots, and code
      analysis.[2]\\nHistory[edit]\\nLangChain was launched in October 2022 as an
      open source project by Harrison Chase, while working at machine learning startup
      Robust Intelligence. In April 2023, LangChain had incorporated and the new startup
      raised over $20 million in funding at a valuation of at least $200 million from
      venture firm Sequoia Capital, a week after announcing a $10 million seed investment
      from Benchmark.[3][4]\\n The project quickly garnered popularity, with improvements
      from hundreds of contributors on GitHub, trending discussions on Twitter, lively
      activity on the project''s Discord server, many YouTube tutorials, and meetups
      in San Francisco and London. As of April 2023, it can read from more than 50
      document types and data sources.[9]\\nReferences[edit]\\nExternal links[edit]\"},
      {\"url\": \"https://www.forbes.com/companies/langchain/\", \"content\": \"LangChain.
      Founded in late 2022, LangChain was an instant hit in Silicon Valley for its
      software that enables anyone to build apps powered by large language models
      like GPT-4 in as little as two ...\"}, {\"url\": \"https://tedai-sanfrancisco.ted.com/speakers-1/harrison-chase-/co-founder-and-ceo,-langchain/\",
      \"content\": \"Harrison Chase, a Harvard graduate in statistics and computer
      science, co-founded LangChain to streamline the development of Language Model
      applications with open-source Python/Typescript packages. Chase''s experience
      includes heading the Machine Learning team at Robust Intelligence, focusing
      on the testing and validation of machine learning models, and leading the entity
      linking team at Kensho ...\"}, {\"url\": \"https://www.crunchbase.com/organization/langchain\",
      \"content\": \"Summary. LangChain may be growing as evidenced by securing $25
      million in funding, which is a significant financial milestone for the company.
      Additionally, the launch of a new platform to facilitate the full lifecycle
      of Large Language Model applications suggests expansion in their product offerings.
      The integration of Gemini Pro API, enabling ...\"}, {\"url\": \"https://craft.co/langchain\",
      \"content\": \"LangChain has 5 employees at their 1 location and $35 m in total
      funding,. See insights on LangChain including office locations, competitors,
      revenue, financials, executives, subsidiaries and more at Craft. ... Harrison
      Chase, Co-Founder and Chief Executive Officer. Linkedin;\"}, {\"url\": \"https://www.langchain.com/about\",
      \"content\": \"Ready to start shipping\\nreliable GenAI apps faster?\\nLangChain
      and LangSmith are critical parts of the reference\\narchitecture to get you
      from prototype to production. Announcing the General Availability of LangSmith\\nToday,
      we\\u2019re thrilled to announce the general availability of LangSmith \\u2014
      our solution for LLM application development, monitoring, and testing.\\n We
      build products that enable developers to go from an idea to working code in
      an afternoon and in the hands of users in days or weeks. The biggest developer
      community in \\u00a0GenAI\\nLearn alongside the 100K+ practitioners\\nwho are
      pushing the industry forward.\\n And we built LangSmith to support all stages
      of the AI engineering lifecycle, to get applications into production faster.\\n\"},
      {\"url\": \"https://www.bluelabellabs.com/blog/what-is-langchain/\", \"content\":
      \"Similar setups to AutoTweeter would allow businesses to parse past and present
      customer queries to understand drop-off points and build the appropriate flows
      to solve customer problems on the spot, instead of spitting them into a queue
      to wait for a human and plot how they\\u2019re going to steal from your business.\\n
      Rather than creating a bot to tweet in your tone on your behalf about your love
      for processed snacks, hockey, and political happenings, you could train such
      a system on your brand voice, knowledge bases, and other resources to create
      a highly valuable feature for your digital touchpoints.\\n Here, you essentially
      create a curated ML system that\\u2019s tailored to your brand with specific
      issues and requests from your customers \\u2013 with a little bit of dev magic,
      you can quickly build a solution that integrates features like payment systems,
      tech support, ordering, account management, and so much more.\\n Creating a
      bot for social media that can post just like you\\nSocial media already has
      enough bots engaging with people as if they\\u2019re real people, but for the
      sake of making a functional example, our co-founder Bobby created a bot called
      AutoTweeter for the social media platform we all still call Twitter.\\n How
      does LangChain work?\\nLangchain essentially serves as a middleman between other
      LLMs, data sources, and API-accessible functions \\u2013 more precisely, it\\u2019s
      a comprehensive open-source library of components that allow users to build
      better (i.e., more custom) solutions by interconnecting different resources.\\n\"},
      {\"url\": \"https://www.latent.space/p/langchain\", \"content\": \"And so you
      can do a lot of things like reflection and kind of like making sure that you''re
      on the right track and really put more safeguards and thinking about these agents
      as opposed to relying on like chas and interface, like the bot we have that''s
      on GitHub answering questions on the issues, I think probably gives better answers
      than the bots that we have that are on chat on the website. [00:50:32]\\nHarrison:
      And then between kind of like allowing for, I think one helpful thing that we
      did in terms of like distinguishing between basically the base interfaces and
      then more complex stuff is part of the separation around the docs is there''s
      like the components piece, which has the model IO, the retrieval, the agents,
      the callbacks, things like that. And then like, I know a lot of people are doing
      this, but like a personal assistant for like managing like email calendar, basic
      stuff, which I think is, I think that''s like a fantastic application for these
      like agent like things, because if you think about personal assistants today,
      you usually interact, I don''t have one, but I''m told you interact with them
      over email. Show Notes\\nLangChain\\nLangChain\\u2019s Berkshire Hathaway Homepage\\nAbstractions
      tweet\\nLangSmith\\nLangSmith Cookbooks repo\\nLangChain Retrieval blog\\nEvaluating
      CSV Question/Answering blog and YouTube\\nMultiOn Partner blog\\nHarvard Sports
      Analytics Collective\\nEvaluating RAG Webinar\\nawesome-langchain:\\nLLM Math
      Chain\\nSelf-Ask\\nLangChain Hub UI\\n\\u201cLangChain is Pointless\\u201d\\nHarrison\\u2019s
      links\\nsports - estimating player compatibility in the NBA\\nearly interest
      in prompt injections\\nGitHub\\nTwitter\\nTimestamps\\n[00:00:00] Introduction\\n[00:00:48]
      Harrison''s background and how sports led him into ML\\n[00:04:54] Memory (for
      long running chats, whether a simple Buffer, Knowledge Graph, Summary, or Vector
      Store)\\nUse-Cases: compositions of Components\\nChains: combining a PromptTemplate,
      LLM Model and optional OutputParser\\nwith Router, Sequential, and Transform
      Chains for advanced usecases\\nsavable, sharable schemas that can be loaded
      from LangChainHub\\nAgents: a chain that has access to a suite of tools, of
      nondeterministic8 length because the LLM is used as a reasoning engine to determine
      which actions to take and in which order.\"}, {\"url\": \"https://www.langchain.com/\",
      \"content\": \"Join Discord\\nContribute on GitHub\\n5.3M\\nMonthly Downloads\\n67k\\nGithub
      Stars\\n1800+\\nContributors\\n34k+\\nApps Powered\\nReady to build?\\nGo To
      Docs\\nGo To Docs\\nGo To Docs\\nContact Sales\\nContact Sales\\nContact Sales\\nProducts
      and use-cases\\nLangChain\\nLangSmith\\nRetrieval\\nAgents\\nInspiration\\nCode\\nGitHub\\nLangChain
      Hub\\nPython Docs\\nJS/TS Docs\\nSocial\\nTwitter\\nDiscord\\nBlog\\nLinkedIn\\nYouTube\\nTerms
      of Service\\nSign up for our newsletter\\nProducts and use-cases\\nLangChain\\nLangSmith\\nRetrieval\\nAgents\\nInspiration\\nCode\\nGitHub\\nLangChain
      Hub\\nPython Docs\\nJS/TS Docs\\nSocial\\nTwitter\\nDiscord\\nBlog\\nLinkedIn\\nYouTube\\nTerms
      of Service\\nSign up for our newsletter\\nProducts and use-cases\\nLangChain\\nLangSmith\\nRetrieval\\nAgents\\nInspiration\\nCode\\nGitHub\\nLangChain
      Hub\\nPython Docs\\nJS/TS Docs\\nSocial\\nTwitter\\nDiscord\\nBlog\\nLinkedIn\\nYouTube\\nTerms
      of Service\\nSign up for our newsletter Chatbots\\nQ&A Over Docs\\nSummarization\\nCopilots\\nWorkflow
      Automation\\nDocument Analysis\\nCustom Search\\nHarness the power\\u2013and
      wrangle the complexity\\u2013 of LLMs\\nGet from proof-of-concept to production
      on one platform\\nRely on a set of tools built especially for non-deterministic
      models\\nLeverage new cognitive architectures and battle-tested orchestration\\nThanks
      to our contributors and partners for making LangChain awesome\\nWhen you build
      with LangChain, you build alongside a community of developers advancing the
      frontier of what\\u2019s possible with LLMs.\\n \\ud83e\\udd9c\\ud83d\\udd17
      LangChain\\nLangSmith\\nLangServe\\nAgents\\nRetrieval\\nEvaluation\\nBlog\\nDocs\\n\\ud83e\\udd9c\\ud83d\\udd17
      LangChain\\n\\ud83e\\udd9c\\ud83d\\udd17 LangChain\\nLangChain Expression Language
      makes it easy to create custom chains.\\n Go To Docs\\nGo To Docs\\nGo To Docs\\nContact
      Sales\\nglobal corporations, STARTUPS, and TINKERERS build with LangChain\\nglobal
      corporations, STARTUPS, and TINKERERS build with LangChain\\nglobal corporations,
      STARTUPS, and TINKERERS build with LangChain\\nA complete set of powerful building
      blocks\\nA complete set of powerful building blocks\\n Try it out here\\nGet
      your LLM application from prototype to production\\nGet your LLM application
      from prototype to production\\nBuild context-aware, reasoning applications with
      LangChain\\u2019s flexible abstractions and AI-first toolkit.\\n\"}]", "tool_use_id":
      "toolu_01U9aJPZLUKVzQPYxMH4f32A", "is_error": false}]}], "model": "claude-3-5-sonnet-20240620",
      "tools": [{"name": "scrape_website", "description": "Scrape and summarize content
      from a given URL.", "input_schema": {"type": "object", "properties": {"url":
      {"type": "string"}}, "required": ["url"]}}, {"name": "search", "description":
      "Search for general results. This function performs a search using the Tavily
      search engine, which is designed\nto provide comprehensive, accurate, and trusted
      results. It''s particularly useful\nfor answering questions about current events.",
      "input_schema": {"type": "object", "properties": {"query": {"type": "string"}},
      "required": ["query"]}}, {"name": "Info", "description": "Call this when you
      have gathered all the relevant info", "input_schema": {"type": "object", "properties":
      {"founder": {"type": "string", "description": "The name of the company founder."},
      "websiteUrl": {"type": "string", "description": "Website URL of the company,
      e.g.: https://openai.com/, or https://microsoft.com"}, "products_sold": {"type":
      "array", "items": {"type": "string"}, "description": "A list of products sold
      by the company."}}, "required": ["founder", "websiteUrl", "products_sold"]}}]}'
    headers: {}
    method: POST
    uri: https://api.anthropic.com/v1/messages
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA4RTXU8bMRD8Kyu/0EqXkAQK1G80FbRqQBG04qGpInO3uTP41od3nSON8t8r34HS
        Sq36ZHm/ZnY83ipbKK1qLpej8czw5eXDLR5f8NX59PmBJmfzj6QyJZsGUxUymxJVpoJ3KWCYLYsh
        UZmqfYFOaZU7EwscHA3eDdgToQwmo8nx6GQyUpnKPQmSKP19+zpU8Dm1d4dWHwxjAZ5AKgRGE/IK
        AnJ0whl8PlgjlEYqDFgA+xrB0sqH2oj1BObeR4GZoXJaGUtDmKEcMHCsaxPsT+xmPuIGGm9JWC9o
        QeMhXPhIBQYNn0wIlj3BtDKMC5oM4Q7v2QrCt5uZhkqkYX142Lbt0Bkq8w4l9/Xhgo6GMA++iLkw
        sHeF3vMA0zRoAoN48KsVBmBcYzAOmtcOQwUwhrXNkSGgM4JFKncmlAgJK5oSoZMY3sxmV2/TUGfz
        bnF+3bSXBmofELjB3K5s/heFpEIb9uD3G+A8mMZS+ZJq+62HSaBr32bgeiFTVa/ifrmX2sS2ROmx
        CxRjHRb/xdZql+2N4L1bRk726jyZ7nE5Gr+/Psf59HbGd/LUfv1yurmZV+2TyhSZOvX1tJYvRFI3
        NVGU3qoYkiH//Wxqt/uRKRbfLAMa9vQniy7B+BSRclSaonOZit0P0NseZSn+EYmVPh6fnGXKR/k9
        OD492+1+AQAA//8DABMQySdiAwAA
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c40d14c993767e3-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 16 Sep 2024 12:21:43 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Robots-Tag:
      - none
      anthropic-ratelimit-requests-limit:
      - '4000'
      anthropic-ratelimit-requests-remaining:
      - '3999'
      anthropic-ratelimit-requests-reset:
      - '2024-09-16T12:21:54Z'
      anthropic-ratelimit-tokens-limit:
      - '400000'
      anthropic-ratelimit-tokens-remaining:
      - '400000'
      anthropic-ratelimit-tokens-reset:
      - '2024-09-16T12:21:43Z'
      request-id:
      - req_01BYbDqKzaEJm78k5yYDfy3e
      via:
      - 1.1 google
      x-cloud-trace-context:
      - c911d857764757f7201c25553134a3cd
    status:
      code: 200
      message: OK
- request:
    body: null
    headers: {}
    method: GET
    uri: https://www.langchain.com/
  response:
    body:
      string: "<!DOCTYPE html><!-- Last Published: Sun Sep 15 2024 15:59:13 GMT+0000
        (Coordinated Universal Time) --><html data-wf-domain=\"www.langchain.com\"
        data-wf-page=\"660189f45f9033dd87f82a27\" data-wf-site=\"65b8cd72835ceeacd4449a53\"
        lang=\"en\"><head><meta charset=\"utf-8\"/><title>LangChain</title><meta content=\"LangChain\u2019s
        suite of products supports developers along each step of their development
        journey.\" name=\"description\"/><meta content=\"LangChain\" property=\"og:title\"/><meta
        content=\"LangChain\u2019s suite of products supports developers along each
        step of their development journey.\" property=\"og:description\"/><meta content=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65c69af1f3035ff97de2cc38_HomePage-metaImage.jpg\"
        property=\"og:image\"/><meta content=\"LangChain\" property=\"twitter:title\"/><meta
        content=\"LangChain\u2019s suite of products supports developers along each
        step of their development journey.\" property=\"twitter:description\"/><meta
        content=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65c69af1f3035ff97de2cc38_HomePage-metaImage.jpg\"
        property=\"twitter:image\"/><meta property=\"og:type\" content=\"website\"/><meta
        content=\"summary_large_image\" name=\"twitter:card\"/><meta content=\"width=device-width,
        initial-scale=1\" name=\"viewport\"/><link href=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/css/langchain-tonik.1c6f06df2.min.css\"
        rel=\"stylesheet\" type=\"text/css\"/><style>@media (min-width:992px) {html.w-mod-js:not(.w-mod-ix)
        [data-w-id=\"8202c0ba-560c-1974-f95f-1e4380661694\"] {opacity:0;}html.w-mod-js:not(.w-mod-ix)
        [data-w-id=\"4f634ffc-aabe-5b88-a321-3a55f4cd08d8\"] {opacity:0;}html.w-mod-js:not(.w-mod-ix)
        [data-w-id=\"00671cb6-faa2-aff9-f3ca-4d15160aa934\"] {opacity:0;}html.w-mod-js:not(.w-mod-ix)
        [data-w-id=\"43123052-e7a7-8a89-24f2-e32f8cae37e1\"] {opacity:0;}html.w-mod-js:not(.w-mod-ix)
        [data-w-id=\"f49f52e7-0f65-8bf9-79c6-b74cb4e015a5\"] {opacity:0;}}@media (max-width:991px)
        and (min-width:768px) {html.w-mod-js:not(.w-mod-ix) [data-w-id=\"4f634ffc-aabe-5b88-a321-3a55f4cd08d8\"]
        {opacity:0;}html.w-mod-js:not(.w-mod-ix) [data-w-id=\"00671cb6-faa2-aff9-f3ca-4d15160aa934\"]
        {opacity:0;}html.w-mod-js:not(.w-mod-ix) [data-w-id=\"43123052-e7a7-8a89-24f2-e32f8cae37e1\"]
        {opacity:0;}html.w-mod-js:not(.w-mod-ix) [data-w-id=\"f49f52e7-0f65-8bf9-79c6-b74cb4e015a5\"]
        {opacity:0;}}</style><script type=\"text/javascript\">!function(o,c){var n=c.documentElement,t=\"
        w-mod-\";n.className+=t+\"js\",(\"ontouchstart\"in o||o.DocumentTouch&&c instanceof
        DocumentTouch)&&(n.className+=t+\"touch\")}(window,document);</script><link
        href=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65c50ca4352352dd6a747e69_favicon.png\"
        rel=\"shortcut icon\" type=\"image/x-icon\"/><link href=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65c50ca94c9fe4ac7c52b415_webClip.png\"
        rel=\"apple-touch-icon\"/><link href=\"https://www.langchain.com/\" rel=\"canonical\"/><!--
        Start cookieyes banner --> \n<script id=\"cookieyes\" type=\"text/javascript\"
        src=\"https://cdn-cookieyes.com/client_data/d2e859934c4309be824ea8bb/script.js\">\n</script>
        \n<!-- End cookieyes banner -->\n<!-- Google tag (gtag.js) -->\n<script async
        src=\"https://www.googletagmanager.com/gtag/js?id=G-V1YFVJYTLP\"></script>\n<script>\n
        \ window.dataLayer = window.dataLayer || [];\n  function gtag(){dataLayer.push(arguments);}\n
        \ gtag('js', new Date());\n\n  gtag('config', 'G-V1YFVJYTLP');\n</script>
        \n\n\n<script defer data-domain=\"langchain.com\" src=\"https://plausible.io/js/script.js\"></script>\n\n\n<!--
        Keep this css code to improve the font quality-->\n<style>\n  * {\n  -webkit-font-smoothing:
        antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  -o-font-smoothing:
        antialiased;\n}\n</style>\n\n<!-- [Attributes by Finsweet] Disable scrolling
        -->\n<script defer src=\"https://cdn.jsdelivr.net/npm/@finsweet/attributes-scrolldisable@1/scrolldisable.js\"></script>\n</head><body><div
        class=\"page-wrapper\"><div class=\"global-styles w-embed\"><style>\n\n* {\n
        \ -webkit-font-smoothing: antialiased;\n}\n\n.page-wrapper {\noverflow: clip;\n
        \ }\n\n\n\n/* Set fluid size change for smaller breakpoints */\n  html { font-size:
        1rem; }\n  @media screen and (max-width:1920px) and (min-width:1281px) { html
        { font-size: calc(0.2499999999999999rem + 0.6250000000000001vw); } }\n  @media
        screen and (max-width:1280px) and (min-width:992px) { html { font-size: calc(0.41223612197028925rem
        + 0.4222048475371384vw); } }\n/* video sizing */\n\nvideo {\n    object-fit:
        fill;\n\t\twidth: 100%;\n}\n\n\n\n#retrieval-video {\n    object-fit: cover;\n
        \   width: 100%;\n}\n\n\n\n/* Set color style to inherit */\n.inherit-color
        * {\n    color: inherit;\n}\n\n/* Focus state style for keyboard navigation
        for the focusable elements */\n*[tabindex]:focus-visible,\n  input[type=\"file\"]:focus-visible
        {\n   outline: 0.125rem solid #4d65ff;\n   outline-offset: 0.125rem;\n}\n\n/*
        Get rid of top margin on first element in any rich text element */\n.w-richtext
        > :not(div):first-child, .w-richtext > div:first-child > :first-child {\n
        \ margin-top: 0 !important;\n}\n\n/* Get rid of bottom margin on last element
        in any rich text element */\n.w-richtext>:last-child, .w-richtext ol li:last-child,
        .w-richtext ul li:last-child {\n\tmargin-bottom: 0 !important;\n}\n\n/* Prevent
        all click and hover interaction with an element */\n.pointer-events-off {\n\tpointer-events:
        none;\n}\n\n/* Enables all click and hover interaction with an element */\n.pointer-events-on
        {\n  pointer-events: auto;\n}\n\n/* Create a class of .div-square which maintains
        a 1:1 dimension of a div */\n.div-square::after {\n\tcontent: \"\";\n\tdisplay:
        block;\n\tpadding-bottom: 100%;\n}\n\n/* Make sure containers never lose their
        center alignment */\n.container-medium,.container-small, .container-large
        {\n\tmargin-right: auto !important;\n  margin-left: auto !important;\n}\n\n/*
        \nMake the following elements inherit typography styles from the parent and
        not have hardcoded values. \nImportant: You will not be able to style for
        example \"All Links\" in Designer with this CSS applied.\nUncomment this CSS
        to use it in the project. Leave this message for future hand-off.\n*/\n/*\na,\n.w-input,\n.w-select,\n.w-tab-link,\n.w-nav-link,\n.w-dropdown-btn,\n.w-dropdown-toggle,\n.w-dropdown-link
        {\n  color: inherit;\n  text-decoration: inherit;\n  font-size: inherit;\n}\n*/\n\n/*
        Apply \"...\" after 3 lines of text */\n.text-style-3lines {\n\tdisplay: -webkit-box;\n\toverflow:
        hidden;\n\t-webkit-line-clamp: 3;\n\t-webkit-box-orient: vertical;\n}\n\n/*
        Apply \"...\" after 2 lines of text */\n.text-style-2lines {\n\tdisplay: -webkit-box;\n\toverflow:
        hidden;\n\t-webkit-line-clamp: 2;\n\t-webkit-box-orient: vertical;\n}\n\n/*
        Adds inline flex display */\n.display-inlineflex {\n  display: inline-flex;\n}\n\n/*
        These classes are never overwritten */\n.hide {\n  display: none !important;\n}\n\n@media
        screen and (max-width: 991px) {\n    .hide, .hide-tablet {\n        display:
        none !important;\n    }\n}\n  @media screen and (max-width: 767px) {\n    .hide-mobile-landscape{\n
        \     display: none !important;\n    }\n}\n  @media screen and (max-width:
        479px) {\n    .hide-mobile{\n      display: none !important;\n    }\n}\n \n.margin-0
        {\n  margin: 0rem !important;\n}\n  \n.padding-0 {\n  padding: 0rem !important;\n}\n\n.spacing-clean
        {\npadding: 0rem !important;\nmargin: 0rem !important;\n}\n\n.margin-top {\n
        \ margin-right: 0rem !important;\n  margin-bottom: 0rem !important;\n  margin-left:
        0rem !important;\n}\n\n.padding-top {\n  padding-right: 0rem !important;\n
        \ padding-bottom: 0rem !important;\n  padding-left: 0rem !important;\n}\n
        \ \n.margin-right {\n  margin-top: 0rem !important;\n  margin-bottom: 0rem
        !important;\n  margin-left: 0rem !important;\n}\n\n.padding-right {\n  padding-top:
        0rem !important;\n  padding-bottom: 0rem !important;\n  padding-left: 0rem
        !important;\n}\n\n.margin-bottom {\n  margin-top: 0rem !important;\n  margin-right:
        0rem !important;\n  margin-left: 0rem !important;\n}\n\n.padding-bottom {\n
        \ padding-top: 0rem !important;\n  padding-right: 0rem !important;\n  padding-left:
        0rem !important;\n}\n\n.margin-left {\n  margin-top: 0rem !important;\n  margin-right:
        0rem !important;\n  margin-bottom: 0rem !important;\n}\n  \n.padding-left
        {\n  padding-top: 0rem !important;\n  padding-right: 0rem !important;\n  padding-bottom:
        0rem !important;\n}\n  \n.margin-horizontal {\n  margin-top: 0rem !important;\n
        \ margin-bottom: 0rem !important;\n}\n\n.padding-horizontal {\n  padding-top:
        0rem !important;\n  padding-bottom: 0rem !important;\n}\n\n.margin-vertical
        {\n  margin-right: 0rem !important;\n  margin-left: 0rem !important;\n}\n
        \ \n.padding-vertical {\n  padding-right: 0rem !important;\n  padding-left:
        0rem !important;\n}\n\n/* Apply \"...\" at 100% width */\n.truncate-width
        { \n\t\twidth: 100%; \n    white-space: nowrap; \n    overflow: hidden; \n
        \   text-overflow: ellipsis; \n}\n/* Removes native scrollbar */\n.no-scrollbar
        {\n    -ms-overflow-style: none;\n    overflow: -moz-scrollbars-none; \n}\n\n.no-scrollbar::-webkit-scrollbar
        {\n    display: none;\n}\n\ninput:checked + span {\ncolor: white    /* styles
        for the div immediately following the checked input */\n}\n\n/* styles for
        word-wrapping\nh1, h2, h3 {\nword-wrap: break-word;\nhyphens: auto;\n}*/\n\n[nav-theme=\"light\"]
        .navbar_logo-svg {\n\t--nav--logo: var(--light--logo);\n}\n\n[nav-theme=\"light\"]
        .button.is-nav {\n\t--nav--button-bg: var(--light--button-bg);\n\t--nav--button-text:
        var(--light--button-text);\n}\n\n[nav-theme=\"light\"] .button.is-nav:hover
        {\n\t--nav--button-bg: var(--dark--button-bg);\n\t--nav--button-text:var(--dark--button-text);\n}\n\n[nav-theme=\"dark\"]
        .navbar_logo-svg {\n\t--nav--logo: var(--dark--logo);\n}\n\n[nav-theme=\"dark\"]
        .button.is-nav {\n\t--nav--button-bg: var(--dark--button-bg);\n\t--nav--button-text:
        var(--dark--button-text);\n}\n\n[nav-theme=\"dark\"] .button.is-nav:hover
        {\n\t--nav--button-bg: var(--light--button-bg);\n\t--nav--button-text: var(--light--button-text);\n}\n\n[nav-theme=\"red\"]
        .navbar_logo-svg {\n\t--nav--logo: var(--red--logo);\n}\n\n\n[nav-theme=\"red\"]
        .button.is-nav {\n\t--nav--button-bg: var(--red--button-bg);\n\t--nav--button-text:
        var(--red--button-text);\n}\n\n.navbar_logo-svg.is-light, .navbar_logo-svg.is-red.is-light{\ncolor:
        #F8F7FF!important;\n}\n\n.news_button[disabled] {\nbackground: none;\n}\n\n.product_bg-video
        video {\nobject-fit: fill;\n}\n\n</style></div><div data-animation=\"default\"
        class=\"navbar_component w-nav\" data-easing2=\"ease\" fs-scrolldisable-element=\"smart-nav\"
        data-easing=\"ease\" data-collapse=\"medium\" data-w-id=\"78839fc1-6b85-b108-b164-82fcae730868\"
        role=\"banner\" data-duration=\"400\"><div class=\"navbar_container\"><a href=\"/\"
        aria-current=\"page\" class=\"navbar_logo-link w-nav-brand w--current\"><div
        class=\"navbar_logo-svg w-embed\"><svg width=\"100%\" height=\"100%\" viewBox=\"0
        0 240 41\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path d=\"M61.5139
        11.1569C60.4527 11.1569 59.4549 11.568 58.708 12.3148L55.6899 15.3248C54.8757
        16.1368 54.4574 17.2643 54.5431 18.4202C54.5492 18.4833 54.5553 18.5464 54.5615
        18.6115C54.6696 19.4988 55.0594 20.2986 55.6899 20.9254C56.1246 21.3589 56.6041
        21.6337 57.1857 21.825C57.2163 22 57.2326 22.177 57.2326 22.3541C57.2326 23.1519
        56.9225 23.9008 56.3592 24.4625L56.1735 24.6477C55.1655 24.3037 54.3247 23.8011
        53.5656 23.044C52.5576 22.0386 51.8903 20.7687 51.6393 19.3747L51.6046 19.1813L51.4515
        19.3055C51.3475 19.3889 51.2495 19.4785 51.1577 19.57L48.1396 22.58C46.5928
        24.1226 46.5928 26.636 48.1396 28.1786C48.913 28.9499 49.9292 29.3366 50.9475
        29.3366C51.9658 29.3366 52.98 28.9499 53.7534 28.1786L56.7715 25.1687C58.3183
        23.626 58.3183 21.1147 56.7715 19.57C56.3592 19.159 55.8675 18.8496 55.3104
        18.6502C55.2798 18.469 55.2634 18.2879 55.2634 18.1109C55.2634 17.2439 55.6063
        16.4217 56.2348 15.7949C57.2449 16.1388 58.1407 16.6965 58.8978 17.4515C59.9038
        18.4548 60.5691 19.7227 60.8241 21.1208L60.8588 21.3141L61.0119 21.19C61.116
        21.1066 61.2139 21.017 61.3078 20.9234L64.3259 17.9135C65.8727 16.3708 65.8747
        13.8575 64.3259 12.3148C63.577 11.568 62.5811 11.1569 61.518 11.1569H61.5139Z\"
        fill=\"CurrentColor\"/>\n<path d=\"M59.8966 0.148865H20.4063C9.15426 0.148865
        0 9.27841 0 20.5001C0 31.7217 9.15426 40.8513 20.4063 40.8513H59.8966C71.1486
        40.8513 80.3029 31.7217 80.3029 20.5001C80.3029 9.27841 71.1486 0.148865 59.8966
        0.148865ZM40.4188 32.0555C39.7678 32.1898 39.0352 32.2142 38.5373 31.6953C38.3536
        32.1165 37.9251 31.8947 37.5945 31.8398C37.5639 31.9252 37.5374 32.0005 37.5088
        32.086C36.4089 32.1593 35.5845 31.04 35.0601 30.1954C34.0193 29.6337 32.8378
        29.2918 31.7746 28.7036C31.7134 29.6724 31.9257 30.8731 31.0012 31.4979C30.9543
        33.36 33.8255 31.7177 34.0887 33.1056C33.8847 33.128 33.6582 33.073 33.4949
        33.2297C32.746 33.9563 31.8869 32.6803 31.0237 33.2074C29.8646 33.7894 29.7483
        34.2656 28.3137 34.3857C28.2342 34.2656 28.2668 34.1862 28.3341 34.113C28.7382
        33.6449 28.7668 33.0934 29.4565 32.8939C28.7464 32.782 28.1525 33.1728 27.5546
        33.4821C26.7771 33.7996 26.7833 32.7657 25.5875 33.537C25.4548 33.4292 25.5181
        33.3315 25.5936 33.2481C25.8976 32.8777 26.2976 32.8227 26.7486 32.8431C24.5304
        31.6098 23.4856 34.3511 22.4612 32.9876C22.1531 33.069 22.0368 33.3457 21.8429
        33.5411C21.6756 33.358 21.8021 33.1361 21.8103 32.9204C21.6103 32.8268 21.3572
        32.782 21.4164 32.4625C21.0246 32.3302 20.7512 32.5622 20.4594 32.782C20.1961
        32.5785 20.6369 32.2814 20.7185 32.0697C20.9532 31.6627 21.4878 31.9863 21.7592
        31.6932C22.5306 31.2557 23.606 31.9659 24.4876 31.8459C25.1671 31.9313 26.0078
        31.2353 25.667 30.5413C24.9406 29.6154 25.0691 28.4045 25.0528 27.2974C24.963
        26.6522 23.4101 25.83 22.9612 25.134C22.4061 24.5072 21.9735 23.7807 21.5409
        23.0664C19.9798 20.0523 20.4716 16.1795 18.5044 13.3812C17.6147 13.8717 16.4556
        13.6397 15.6884 12.9823C15.2741 13.3588 15.2557 13.8513 15.2231 14.3744C14.2293
        13.3833 14.3538 11.5109 15.1476 10.4079C15.4721 9.97239 15.8598 9.61421 16.2924
        9.29876C16.3903 9.22754 16.423 9.15834 16.4209 9.04844C17.2066 5.52362 22.5653
        6.20335 24.259 8.70044C25.4875 10.237 25.8589 12.27 27.2526 13.6967C29.1279
        15.744 31.2645 17.5471 32.9949 19.7267C34.6315 21.7191 35.8008 24.0554 36.8211
        26.4101C37.2374 27.1915 37.2415 28.1501 37.8578 28.8176C38.1618 29.2206 39.6474
        30.3175 39.325 30.7062C39.5107 31.1091 40.8983 31.6647 40.4167 32.0555H40.4188ZM66.4449
        20.032L63.4269 23.0419C62.6228 23.8438 61.6291 24.4421 60.5516 24.7697L60.4965
        24.786L60.4761 24.8389C60.1251 25.7629 59.5925 26.5871 58.8905 27.2852L55.8724
        30.2951C54.5562 31.6078 52.8054 32.3302 50.9402 32.3302C49.0751 32.3302 47.3242
        31.6078 46.008 30.2951C43.2879 27.5823 43.2879 23.1701 46.008 20.4573L49.0261
        17.4474C49.8342 16.6415 50.8015 16.0615 51.8973 15.7257L51.9524 15.7094L51.9728
        15.6565C52.3238 14.7325 52.8584 13.9063 53.5625 13.2021L56.5805 10.1922C57.8967
        8.87953 59.6476 8.15706 61.5127 8.15706C63.3779 8.15706 65.1287 8.87953 66.4449
        10.1922C67.7612 11.5048 68.4856 13.251 68.4856 15.1111C68.4856 16.9712 67.7612
        18.7193 66.4449 20.03V20.032Z\" fill=\"CurrentColor\"/>\n<path d=\"M28.1422
        28.4126C27.8769 29.4424 27.7912 31.1946 26.4485 31.2455C26.3383 31.8398 26.8607
        32.0636 27.3382 31.8723C27.8096 31.6566 28.034 32.0433 28.1932 32.4279C28.9217
        32.5337 29.9992 32.1857 30.04 31.3249C28.9523 30.7001 28.6156 29.5116 28.1442
        28.4106L28.1422 28.4126Z\" fill=\"CurrentColor\"/>\n<path d=\"M99.2089 10.8162H95.624V32.5623H111V29.0983H99.2089V10.8162Z\"
        fill=\"CurrentColor\"/>\n<path d=\"M129.021 32.5623H132.606V32.5236H132.653L132.66
        32.364C132.661 32.3173 132.674 31.8821 132.608 31.2115V23.1182C132.608 20.0733
        134.828 18.6871 136.891 18.6871C139.11 18.6871 140.19 19.8831 140.19 22.3445V32.5623H143.775V21.8674C143.775
        17.8054 141.194 15.2812 137.041 15.2812C135.276 15.2812 133.701 15.7825 132.464
        16.7351L132.431 15.5794H129.026V32.5623H129.021Z\" fill=\"CurrentColor\"/>\n<path
        d=\"M158.365 16.827C157.119 15.8147 155.517 15.2812 153.71 15.2812C148.876
        15.2812 145.873 18.6146 145.873 23.9806C145.873 29.3466 148.876 32.7106 153.71
        32.7106C155.415 32.7106 156.937 32.2464 158.136 31.363C158.033 33.9953 156.384
        35.5588 153.681 35.5588C151.408 35.5588 150.122 34.8351 149.858 33.4085L149.825
        33.2312L146.342 34.2935L146.373 34.4369C146.961 37.2722 149.614 38.9647 153.473
        38.9647C156.091 38.9647 158.144 38.2523 159.577 36.8451C161.023 35.4266 161.755
        33.3827 161.755 30.7715V15.5794H158.439L158.367 16.827H158.365ZM158.141 24.1305C158.141
        27.3704 156.563 29.3063 153.918 29.3063C151.083 29.3063 149.458 27.3655 149.458
        23.9822C149.458 20.5988 151.084 18.6871 153.918 18.6871C156.499 18.6871 158.115
        20.6133 158.141 23.713V24.1305Z\" fill=\"CurrentColor\"/>\n<path d=\"M179.418
        25.6665C178.675 28.0698 176.767 29.3948 174.047 29.3948C170.158 29.3948 167.74
        26.4418 167.74 21.6884C167.74 16.9349 170.179 13.9819 174.107 13.9819C176.825
        13.9819 178.391 15.0474 179.188 17.4346L179.56 18.5516L182.945 16.9623L182.627
        16.0677C181.338 12.4361 178.361 10.5179 174.018 10.5179C171.067 10.5179 168.562
        11.5834 166.773 13.5983C165.003 15.5938 164.066 18.3904 164.066 21.69C164.066
        28.476 167.983 32.862 174.047 32.862C178.32 32.862 181.63 30.6263 182.9 26.8803L183.224
        25.9228L179.747 24.601L179.416 25.6681L179.418 25.6665Z\" fill=\"CurrentColor\"/>\n<path
        d=\"M192.806 15.2812C191.094 15.2812 189.571 15.7503 188.375 16.6417V8.73212H184.79V32.5639H188.375V23.1199C188.375
        20.0557 190.594 18.6598 192.658 18.6598C194.877 18.6598 195.957 19.8558 195.957
        22.3171V32.5656H199.542V21.84C199.542 17.857 196.899 15.2845 192.808 15.2845L192.806
        15.2812Z\" fill=\"CurrentColor\"/>\n<path d=\"M220.496 8.21954C219.164 8.21954
        218.197 9.18668 218.197 10.5181C218.197 11.8495 219.164 12.8167 220.496 12.8167C221.827
        12.8167 222.794 11.8495 222.794 10.5181C222.794 9.18668 221.827 8.21954 220.496
        8.21954Z\" fill=\"CurrentColor\"/>\n<path d=\"M233.262 15.2812C231.497 15.2812
        229.923 15.7825 228.686 16.7351L228.652 15.5794H225.248V32.5623H228.833V23.1182C228.833
        20.0733 231.052 18.6871 233.116 18.6871C235.335 18.6871 236.415 19.8831 236.415
        22.3445V32.5623H240V21.8674C240 17.8054 237.419 15.2812 233.266 15.2812H233.262Z\"
        fill=\"CurrentColor\"/>\n<path d=\"M222.237 15.5794H218.67V23.9984C217.68
        23.1666 216.514 22.538 215.198 22.127V21.271C215.198 17.5202 212.736 15.2812
        208.611 15.2812C205.26 15.2812 202.754 16.8545 201.733 19.5963L201.458 20.3361L204.332
        22.4542L204.825 21.1695C205.475 19.4754 206.679 18.6856 208.611 18.6856C210.544
        18.6856 211.613 19.6156 211.613 21.4483V21.5435C211.498 21.5386 211.382 21.5354
        211.266 21.5338C207.428 21.4725 204.628 22.3704 202.944 24.1982C201.221 26.068
        201.372 28.1683 201.395 28.4004L201.411 28.5616H201.427C201.695 31.1777 203.98
        32.8573 207.301 32.8573C209.13 32.8573 210.82 32.348 212.098 31.4179L212.112
        32.5607H215.198V27.2479L215.131 27.1996C214.695 26.8804 213.934 26.4678 212.789
        26.3227C212.43 26.2776 212.087 26.255 211.771 26.2615H211.611V26.7499C211.611
        27.8734 210.32 29.4546 207.448 29.4546C205.328 29.4546 205.012 28.5632 205.012
        28.0313V27.9765C205.028 27.738 205.128 27.1544 205.655 26.6112C206.324 25.9197
        207.83 25.1106 211.207 25.1622C213.652 25.2008 215.528 25.8843 216.784 27.1947C218.325
        28.8034 218.615 31.0053 218.668 31.8676V32.5623H222.235V15.5794H222.237Z\"
        fill=\"CurrentColor\"/>\n<path d=\"M119.752 15.2071C116.401 15.2071 113.895
        16.7803 112.874 19.5221L112.599 20.262L115.473 22.38L115.966 21.0953C116.616
        19.4012 117.82 18.6114 119.752 18.6114C121.685 18.6114 122.754 19.5415 122.754
        21.3742V21.8046L119.062 22.4558C114.734 23.2214 112.539 25.0719 112.539 27.9539C112.539
        30.836 114.855 32.7832 118.44 32.7832C120.27 32.7832 121.959 32.2738 123.237
        31.3437L123.252 32.4866H126.337V21.1969C126.337 17.446 123.875 15.2071 119.751
        15.2071H119.752ZM122.754 25.1638V26.6773C122.754 27.8008 121.462 29.3821 118.59
        29.3821C116.47 29.3821 116.155 28.4907 116.155 27.9588C116.155 27.4865 116.155
        26.3807 119.91 25.6844L122.754 25.1654V25.1638Z\" fill=\"CurrentColor\"/>\n</svg></div></a><nav
        role=\"navigation\" class=\"navbar_menu is-page-height-tablet w-nav-menu\"><div
        class=\"navbar_navlinks\"><div data-hover=\"true\" data-delay=\"800\" data-w-id=\"86f16cab-d26d-6ec0-1e8b-7f5d62764942\"
        class=\"navbar_menu-dropdown w-dropdown\"><div class=\"navbar_dropdown-toggle
        w-dropdown-toggle\"><div>Products</div><div class=\"dropdown-chevron w-embed\"><svg
        width=\"100%\" height=\"100%\" viewBox=\"0 0 16 9\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path
        d=\"M1.5 1L8 7.5L14.5 1\" stroke=\"CurrentColor\" stroke-width=\"2\"/>\n</svg></div></div><nav
        class=\"navbar_dropdown-list w-dropdown-list\"><div class=\"navbar_dropdown-link-list\"><div
        id=\"w-node-_86f16cab-d26d-6ec0-1e8b-7f5d62764949-62764940\" class=\"dropdown_links-wrap
        is-logos\"><a href=\"/langchain\" class=\"navbar_navlink\">LangChain</a><a
        href=\"/langsmith\" class=\"navbar_navlink\">LangSmith</a><a href=\"/langgraph\"
        class=\"navbar_navlink\">LangGraph</a></div></div></nav></div><div data-hover=\"true\"
        data-delay=\"200\" data-w-id=\"86f16cab-d26d-6ec0-1e8b-7f5d62764950\" class=\"navbar_menu-dropdown
        w-dropdown\"><div class=\"navbar_dropdown-toggle w-dropdown-toggle\"><div>Methods</div><div
        class=\"dropdown-chevron w-embed\"><svg width=\"100%\" height=\"100%\" viewBox=\"0
        0 16 9\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path d=\"M1.5
        1L8 7.5L14.5 1\" stroke=\"CurrentColor\" stroke-width=\"2\"/>\n</svg></div></div><nav
        class=\"navbar_dropdown-list w-dropdown-list\"><div class=\"navbar_dropdown-link-list\"><div
        id=\"w-node-_86f16cab-d26d-6ec0-1e8b-7f5d62764957-62764940\" class=\"dropdown_links-wrap\"><a
        id=\"w-node-_86f16cab-d26d-6ec0-1e8b-7f5d62764958-62764940\" href=\"/retrieval\"
        class=\"navbar_navlink\">Retrieval</a><a id=\"w-node-_86f16cab-d26d-6ec0-1e8b-7f5d6276495a-62764940\"
        href=\"/agents\" class=\"navbar_navlink\">Agents</a><a id=\"w-node-_86f16cab-d26d-6ec0-1e8b-7f5d6276495c-62764940\"
        href=\"/evaluation\" class=\"navbar_navlink\">Evaluation</a></div></div></nav></div><div
        data-hover=\"true\" data-delay=\"200\" data-w-id=\"86f16cab-d26d-6ec0-1e8b-7f5d6276495e\"
        class=\"navbar_menu-dropdown w-dropdown\"><div class=\"navbar_dropdown-toggle
        w-dropdown-toggle\"><div>Resources</div><div class=\"dropdown-chevron w-embed\"><svg
        width=\"100%\" height=\"100%\" viewBox=\"0 0 16 9\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path
        d=\"M1.5 1L8 7.5L14.5 1\" stroke=\"CurrentColor\" stroke-width=\"2\"/>\n</svg></div></div><nav
        class=\"navbar_dropdown-list w-dropdown-list\"><div class=\"navbar_dropdown-link-list\"><div
        id=\"w-node-_51e31229-b145-848e-f1dc-e92c526909bc-62764940\" class=\"dropdown_links-wrap\"><a
        id=\"w-node-_51e31229-b145-848e-f1dc-e92c526909bd-62764940\" href=\"https://blog.langchain.dev/\"
        target=\"_blank\" class=\"navbar_navlink\">Blog</a><a id=\"w-node-_51e31229-b145-848e-f1dc-e92c526909bf-62764940\"
        href=\"/case-studies\" class=\"navbar_navlink\">Case Studies</a><a id=\"w-node-_51e31229-b145-848e-f1dc-e92c526909c1-62764940\"
        href=\"https://academy.langchain.com/\" class=\"navbar_navlink\">LangChain
        Academy</a><a id=\"w-node-_504adce7-2ba3-85f7-90da-f3a703f2243a-62764940\"
        href=\"https://lu.ma/langchain\" target=\"_blank\" class=\"navbar_navlink\">Events</a><a
        id=\"w-node-_51e31229-b145-848e-f1dc-e92c526909c3-62764940\" href=\"/experts\"
        class=\"navbar_navlink\">Experts</a><a id=\"w-node-_51e31229-b145-848e-f1dc-e92c526909c5-62764940\"
        href=\"https://changelog.langchain.com/\" target=\"_blank\" class=\"navbar_navlink\">Changelog</a></div></div></nav></div><div
        data-hover=\"true\" data-delay=\"200\" data-w-id=\"86f16cab-d26d-6ec0-1e8b-7f5d6276497c\"
        class=\"navbar_menu-dropdown w-dropdown\"><div class=\"navbar_dropdown-toggle
        w-dropdown-toggle\"><div>Docs</div><div class=\"dropdown-chevron w-embed\"><svg
        width=\"100%\" height=\"100%\" viewBox=\"0 0 16 9\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path
        d=\"M1.5 1L8 7.5L14.5 1\" stroke=\"CurrentColor\" stroke-width=\"2\"/>\n</svg></div></div><nav
        class=\"navbar_dropdown-list w-dropdown-list\"><div class=\"navbar_dropdown-link-list
        top\"><div id=\"w-node-e5c2bc0a-1422-8e6e-28af-2dbdd9cff1f5-62764940\" class=\"text-size-small
        text-weight-semibold\">Python</div><div id=\"w-node-e5c2bc0a-1422-8e6e-28af-2dbdd9cff1f7-62764940\"
        class=\"dropdown_links-wrap\"><a id=\"w-node-_1ff6dbe6-7771-84f4-414b-58f61076d9ed-62764940\"
        href=\"https://python.langchain.com/v0.2/docs/introduction/\" target=\"_blank\"
        class=\"navbar_navlink\">LangChain</a><a id=\"w-node-_4df7c45a-9a40-888d-5455-7107ee4cd4a3-62764940\"
        href=\"https://docs.smith.langchain.com/\" target=\"_blank\" class=\"navbar_navlink\">LangSmith</a><a
        id=\"w-node-_8e20603b-52bd-d3aa-eb3f-c4cf86c29851-62764940\" href=\"https://langchain-ai.github.io/langgraph/tutorials/introduction/\"
        target=\"_blank\" class=\"navbar_navlink\">LangGraph</a></div></div><div class=\"navbar_dropdown-link-list
        bottom\"><div id=\"w-node-a3462865-b5b0-23ec-62f2-d982c6254db9-62764940\"
        class=\"text-size-small text-weight-semibold\">JavaScript</div><div id=\"w-node-a3462865-b5b0-23ec-62f2-d982c6254dbb-62764940\"
        class=\"dropdown_links-wrap\"><a id=\"w-node-_6f08623a-c042-fd92-80b0-6af2464dde5b-62764940\"
        href=\"https://js.langchain.com/v0.2/docs/tutorials/\" target=\"_blank\" class=\"navbar_navlink\">LangChain</a><a
        id=\"w-node-_35b67bfd-02a3-b2d3-0ef5-149e574bcd3a-62764940\" href=\"https://docs.smith.langchain.com/\"
        target=\"_blank\" class=\"navbar_navlink\">LangSmith</a><a id=\"w-node-_3e895595-200d-8649-01f6-24dfab935ded-62764940\"
        href=\"https://langchain-ai.github.io/langgraphjs/tutorials/quickstart/\"
        target=\"_blank\" class=\"navbar_navlink\">LangGraph</a></div></div></nav></div><div
        data-hover=\"true\" data-delay=\"200\" data-w-id=\"86f16cab-d26d-6ec0-1e8b-7f5d6276498a\"
        class=\"navbar_menu-dropdown w-dropdown\"><div class=\"navbar_dropdown-toggle
        w-dropdown-toggle\"><div>Company</div><div class=\"dropdown-chevron w-embed\"><svg
        width=\"100%\" height=\"100%\" viewBox=\"0 0 16 9\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path
        d=\"M1.5 1L8 7.5L14.5 1\" stroke=\"CurrentColor\" stroke-width=\"2\"/>\n</svg></div></div><nav
        class=\"navbar_dropdown-list w-dropdown-list\"><div class=\"navbar_dropdown-link-list\"><div
        id=\"w-node-_86f16cab-d26d-6ec0-1e8b-7f5d62764991-62764940\" class=\"dropdown_links-wrap\"><a
        id=\"w-node-_86f16cab-d26d-6ec0-1e8b-7f5d62764992-62764940\" href=\"/about\"
        class=\"navbar_navlink\">About</a><a id=\"w-node-_86f16cab-d26d-6ec0-1e8b-7f5d62764994-62764940\"
        href=\"/careers\" class=\"navbar_navlink\">Careers</a><link rel=\"prefetch\"
        href=\"/careers\"/></div></div></nav></div><a href=\"/pricing\" class=\"navbar_link
        is-pricing menu\">Pricing</a></div><div class=\"navbar_menu-buttons\"><a href=\"/contact-sales\"
        class=\"button is-nav-copy w-button\">Get a demo</a></div><div class=\"navbar_menu-buttons\"><a
        href=\"https://smith.langchain.com/\" target=\"_blank\" class=\"button is-nav
        w-button\">Sign up</a></div></nav><div id=\"menu-toggle\" class=\"navbar_menu-button
        w-nav-button\"><div class=\"menu-icon\"><div class=\"menu-icon_line-top\"></div><div
        class=\"menu-icon_line-middle\"><div class=\"menu-icon_line-middle-inner\"></div></div><div
        class=\"menu-icon_line-bottom\"></div></div></div></div><div class=\"navbar_menu-bg\"></div><div
        class=\"navbar_scroll-bg\"></div></div><main class=\"main-wrapper\"><header
        class=\"section_header\"><div class=\"padding-global\"><div class=\"container-large\"><div
        class=\"header_content\"><div class=\"header_title-wrap is-home\"><div class=\"maxwidth-header-home\"><p
        class=\"text-size-large hide\">LangChain\u2019s suite of products supports
        developers along each step of the LLM application lifecycle.</p></div><h1
        data-w-id=\"8202c0ba-560c-1974-f95f-1e4380661694\">Applications that can reason.
        Powered by LangChain.</h1></div><div class=\"join-us_buttons-wrapper\"><a
        href=\"/contact-sales\" class=\"button w-button\">Get a demo</a><a href=\"https://smith.langchain.com/\"
        target=\"_blank\" class=\"button is-alternate w-button\">Sign up for free</a></div></div></div></div><div
        class=\"header_background-video-wrapper\"><div class=\"video-wrap w-embed\"><video
        autoplay muted loop playsinline id=\"myVideo\" poster=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65c8ed3e7d491e37259a30c5_Langchain-hero-1_1706794335%201-placeholder.jpg\">\n
        \ <source data-src=\"https://customer-xp1a3vy0ydc4ega7.cloudflarestream.com/bb6cf069546e3d829aa5808ac8b07748/downloads/default.mp4\"
        type=\"video/mp4\">\n</video></div></div></header><section class=\"section_home-logos\"><div
        class=\"padding-global\"><div class=\"container-large\"><div class=\"home-centered-logos\"><h2
        data-w-id=\"4f634ffc-aabe-5b88-a321-3a55f4cd08d8\">From startups to global
        enterprises, <br/>ambitious builders choose <br/>LangChain products.</h2></div></div></div><div
        data-w-id=\"e0bd309c-bace-a92d-af76-e546c1bb3f8c\" style=\"opacity:0\" class=\"logo_component
        is-home\"><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ca3b7c22746faa78338532_logo_Ally.svg\"
        loading=\"lazy\" width=\"35.5\" alt=\"\" class=\"logo_logo ally\"/><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ca3b7c08e67bb7eefba4c2_logo_Rakuten.svg\"
        loading=\"lazy\" alt=\"\" class=\"logo_logo\"/><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ca3b7c576fdde32d03c1a0_logo_Elastic.svg\"
        loading=\"lazy\" alt=\"\" class=\"logo_logo\"/><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ca3b7c6d5592036dae24e5_logo_BCG.svg\"
        loading=\"lazy\" alt=\"\" class=\"logo_logo\"/><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ca3b7cbcf6473519b06d84_logo_IDEO.svg\"
        loading=\"lazy\" alt=\"\" class=\"logo_logo\"/><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/6606183e52d49bc369acc76c_mdy_logo_rgb_moodysblue.png\"
        loading=\"lazy\" width=\"66\" sizes=\"(max-width: 991px) 95.953125px, (max-width:
        1279px) 9vw, (max-width: 1439px) 7vw, 95.953125px\" alt=\"\" srcset=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/6606183e52d49bc369acc76c_mdy_logo_rgb_moodysblue-p-500.png
        500w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/6606183e52d49bc369acc76c_mdy_logo_rgb_moodysblue-p-800.png
        800w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/6606183e52d49bc369acc76c_mdy_logo_rgb_moodysblue-p-1080.png
        1080w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/6606183e52d49bc369acc76c_mdy_logo_rgb_moodysblue-p-1600.png
        1600w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/6606183e52d49bc369acc76c_mdy_logo_rgb_moodysblue-p-2000.png
        2000w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/6606183e52d49bc369acc76c_mdy_logo_rgb_moodysblue-p-2600.png
        2600w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/6606183e52d49bc369acc76c_mdy_logo_rgb_moodysblue-p-3200.png
        3200w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/6606183e52d49bc369acc76c_mdy_logo_rgb_moodysblue.png
        4086w\" class=\"logo_logo\"/><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ca3b7c8ad7db6ed6ec611e_logo_Adyen.svg\"
        loading=\"lazy\" width=\"72.5\" alt=\"\" class=\"logo_logo\"/><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ca3b7c737d50036a62768b_logo_Infor.svg\"
        loading=\"lazy\" width=\"38\" alt=\"\" class=\"logo_logo infor\"/><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/66d25beedcf257853b52eb88_eventbrite.png\"
        loading=\"lazy\" width=\"38\" sizes=\"(max-width: 991px) 95.953125px, (max-width:
        1279px) 7vw, (max-width: 1439px) 6vw, (max-width: 1919px) 5vw, 95.953125px\"
        alt=\"\" srcset=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/66d25beedcf257853b52eb88_eventbrite-p-500.png
        500w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/66d25beedcf257853b52eb88_eventbrite.png
        800w\" class=\"logo_logo infor\"/><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667f59d98444a5f98aabe21c_acxiom-vector-logo-2022%201.png\"
        loading=\"lazy\" width=\"38\" sizes=\"(max-width: 991px) 95.953125px, (max-width:
        1279px) 9vw, (max-width: 1439px) 7vw, 95.953125px\" alt=\"\" srcset=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667f59d98444a5f98aabe21c_acxiom-vector-logo-2022%201-p-500.png
        500w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667f59d98444a5f98aabe21c_acxiom-vector-logo-2022%201.png
        652w\" class=\"logo_logo\"/><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ca3b7c09a158ffeaab0bd2_logo_Replit.svg\"
        loading=\"lazy\" width=\"86\" alt=\"\" class=\"logo_logo\"/><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ca3b7c9d2b23d292a0cab0_logo_Retool.svg\"
        loading=\"lazy\" width=\"76.5\" alt=\"\" class=\"logo_logo\"/><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ca3b7c44e67a3d0a996bf3_logo_Databricks.svg\"
        loading=\"lazy\" width=\"98\" alt=\"\" class=\"logo_logo\"/><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667f5a1299d6ba453c78a849_image%20(19).png\"
        loading=\"lazy\" width=\"84\" sizes=\"(max-width: 991px) 95.953125px, (max-width:
        1279px) 9vw, (max-width: 1439px) 7vw, 95.953125px\" alt=\"\" srcset=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667f5a1299d6ba453c78a849_image%20(19)-p-500.png
        500w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667f5a1299d6ba453c78a849_image%20(19).png
        600w\" class=\"logo_logo\"/><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ca3b7c63af578816bafcc3_logo_Instacart.svg\"
        loading=\"lazy\" width=\"72.5\" alt=\"\" class=\"logo_logo\"/><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/665dc1dabc940168384d9596_podium%20logo.svg\"
        loading=\"lazy\" width=\"72.5\" height=\"Auto\" alt=\"\" class=\"logo_logo\"/><img
        src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667f19528c3557c2c19c3086_the-home-depot-2%201.png\"
        loading=\"lazy\" sizes=\"(max-width: 991px) 95.953125px, (max-width: 1279px)
        7vw, (max-width: 1439px) 6vw, (max-width: 1919px) 5vw, 95.953125px\" alt=\"\"
        width=\"Auto\" srcset=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667f19528c3557c2c19c3086_the-home-depot-2%201-p-500.png
        500w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667f19528c3557c2c19c3086_the-home-depot-2%201-p-800.png
        800w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667f19528c3557c2c19c3086_the-home-depot-2%201-p-1080.png
        1080w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667f19528c3557c2c19c3086_the-home-depot-2%201-p-1600.png
        1600w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667f19528c3557c2c19c3086_the-home-depot-2%201-p-2000.png
        2000w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667f19528c3557c2c19c3086_the-home-depot-2%201.png
        2500w\" class=\"logo_logo home\"/><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/66c4b0b2399590cbe63b0b3c_Vector%20(1).png\"
        loading=\"lazy\" alt=\"\" class=\"logo_logo bridge\"/></div></section><section
        class=\"section_build-observe-deploy\"><div class=\"padding-global\"><div
        class=\"container-large\"><div class=\"bod_wrapper\"><div class=\"bud_single\"><div
        class=\"bud_top-border\"></div><div class=\"bud_text\"><p class=\"heading-style-decorative-1
        is-alternate\">Build</p><p>LangChain is a framework to build with LLMs by
        chaining interoperable components. LangGraph is the framework for building
        controllable agentic workflows.</p></div><div class=\"bud_bottom-border\"></div></div><div
        class=\"bud_single\"><div class=\"bud_top-border\"></div><div class=\"bud_text\"><p
        class=\"heading-style-decorative-1 is-alternate\">Run</p><p>Deploy your LLM
        applications at scale with LangGraph Cloud, our infrastructure purpose-built
        for agents.</p></div><div class=\"bud_bottom-border\"></div></div><div class=\"bud_single\"><div
        class=\"bud_top-border\"></div><div class=\"bud_text\"><p class=\"heading-style-decorative-1
        is-alternate\">Manage</p><p>Debug, collaborate, test, and monitor your LLM
        app in LangSmith - whether it&#x27;s built with a LangChain framework or not.\_</p></div><div
        class=\"bud_bottom-border\"></div></div></div></div></div></section><section
        class=\"section_home-products\"><div class=\"padding-global\"><div class=\"container-xlarge\"><div
        class=\"home_products-wrapper\"><div class=\"w-layout-grid product_component
        is-inverted\"><div class=\"home_build-video-wrapper\"><div data-poster-url=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ce012c99a9683732d528cf_retrieval-video-poster-00001.jpg\"
        data-video-urls=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ce012c99a9683732d528cf_retrieval-video-transcode.mp4,https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ce012c99a9683732d528cf_retrieval-video-transcode.webm\"
        data-autoplay=\"true\" data-loop=\"true\" data-wf-ignore=\"true\" class=\"product_bg-video
        w-background-video w-background-video-atom\"><video id=\"d01e6884-4b74-66ba-e25f-e5027d12b585-video\"
        autoplay=\"\" loop=\"\" style=\"background-image:url(&quot;https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ce012c99a9683732d528cf_retrieval-video-poster-00001.jpg&quot;)\"
        muted=\"\" playsinline=\"\" data-wf-ignore=\"true\" data-object-fit=\"cover\"><source
        src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ce012c99a9683732d528cf_retrieval-video-transcode.mp4\"
        data-wf-ignore=\"true\"/><source src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ce012c99a9683732d528cf_retrieval-video-transcode.webm\"
        data-wf-ignore=\"true\"/></video></div></div><div id=\"w-node-_1ed64ebd-ae61-1759-b42b-07a447b5c121-87f82a27\"
        class=\"product_text-wrapper\"><div class=\"product_text-content\"><div class=\"icon-embed-custom
        w-embed\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"100%\" height=\"100%\"
        viewBox=\"0 0 77 39\" fill=\"none\" preserveAspectRatio=\"xMidYMid meet\"
        aria-hidden=\"true\" role=\"img\">\n<path d=\"M58.9837 10.5483C57.9651 10.5483
        57.0112 10.9424 56.2919 11.657L53.399 14.5423C52.6171 15.3215 52.2172 16.4012
        52.2999 17.5077C52.3066 17.5678 52.3111 17.6301 52.3178 17.6903C52.4228 18.5407
        52.7958 19.3065 53.399 19.9076C53.8167 20.3239 54.3305 20.6401 54.8867 20.8226C54.9158
        20.9896 54.9314 21.161 54.9314 21.3302C54.9314 22.0938 54.6343 22.8107 54.0937
        23.3495L53.915 23.5276C52.9477 23.1981 52.0899 22.6638 51.3594 21.938C50.3922
        20.974 49.7533 19.7585 49.512 18.4227L49.4785 18.2357L49.3311 18.3559C49.2328
        18.4361 49.1367 18.5229 49.0496 18.6097L46.1545 21.495C44.6712 22.9732 44.6712
        25.3798 46.1545 26.8603C46.8961 27.5994 47.8723 27.969 48.8463 27.969C49.8203
        27.969 50.7965 27.5994 51.5381 26.8603L54.4333 23.975C55.9166 22.4968 55.9166
        20.0902 54.4333 18.6119C54.0379 18.2179 53.5665 17.9218 53.0326 17.7303C53.0036
        17.5567 52.9879 17.383 52.9879 17.2138C52.9879 16.3834 53.3186 15.5953 53.9195
        14.9942C54.889 15.3237 55.7468 15.858 56.4728 16.5816C57.4379 17.5433 58.0767
        18.7589 58.3202 20.0969L58.3538 20.2839L58.5012 20.1637C58.5995 20.0835 58.6955
        19.9967 58.7849 19.9076L61.68 17.0246C63.1633 15.5463 63.1633 13.1397 61.68
        11.6593C60.9629 10.9446 60.0068 10.5506 58.9882 10.5506L58.9837 10.5483Z\"
        fill=\"currentColor\"/>\n<path d=\"M57.4333 0H19.5667C8.77696 0 0 8.74706
        0 19.5C0 30.2529 8.77696 39 19.5667 39H57.4333C68.223 39 77 30.2529 77 19.5C77
        8.74706 68.223 0 57.4333 0ZM38.7558 30.5735C38.1303 30.7027 37.4288 30.7271
        36.953 30.2285C36.7766 30.6314 36.3677 30.4199 36.0483 30.3665C36.0193 30.4489
        35.9947 30.5201 35.9656 30.6025C34.9113 30.6737 34.1205 29.6006 33.6178 28.7903C32.6193
        28.2537 31.4867 27.9243 30.468 27.361C30.4077 28.2894 30.6132 29.4381 29.7264
        30.0392C29.6817 31.8225 32.4361 30.2485 32.6863 31.5798C32.4897 31.5998 32.273
        31.5486 32.1167 31.7C31.3996 32.3969 30.573 31.1746 29.7465 31.6778C28.6363
        32.2366 28.5223 32.6907 27.1485 32.8065C27.0725 32.6929 27.1038 32.615 27.1686
        32.546C27.555 32.0985 27.5819 31.5687 28.2453 31.3772C27.564 31.2704 26.9943
        31.6444 26.4225 31.9427C25.6763 32.2455 25.683 31.257 24.5348 31.9939C24.4075
        31.8915 24.4678 31.798 24.5415 31.7178C24.8342 31.3639 25.2162 31.3104 25.6495
        31.3305C23.5229 30.1483 22.5221 32.7753 21.5392 31.4707C21.2443 31.5486 21.1326
        31.8136 20.9472 32.0028C20.7864 31.8269 20.9092 31.6154 20.9159 31.4084C20.7238
        31.3193 20.4803 31.2748 20.5384 30.9698C20.1609 30.8429 19.9017 31.0655 19.6225
        31.277C19.3701 31.0833 19.7923 30.7962 19.8705 30.5936C20.0961 30.2017 20.6076
        30.5134 20.869 30.2329C21.6084 29.8144 22.6405 30.4956 23.4849 30.3798C24.1372
        30.4622 24.9436 29.7943 24.6152 29.1309C23.9183 28.2448 24.0434 27.0849 24.0277
        26.023C23.9406 25.4063 22.4528 24.6182 22.0217 23.9503C21.49 23.3492 21.0745
        22.6546 20.6613 21.9689C19.1646 19.0815 19.6359 15.3702 17.7505 12.6876C16.8972
        13.1573 15.7869 12.9347 15.0497 12.3047C14.6521 12.6653 14.6342 13.1373 14.6029
        13.6382C13.6513 12.6898 13.7697 10.8954 14.5315 9.83794C14.842 9.4194 15.2128
        9.07655 15.6305 8.776C15.7244 8.70921 15.7556 8.6402 15.7534 8.53556C16.5062
        5.15829 21.6442 5.8106 23.2704 8.20162C24.4499 9.67319 24.8051 11.6212 26.141
        12.9904C27.9393 14.9517 29.9878 16.6793 31.6475 18.7698C33.2157 20.6799 34.3394
        22.9173 35.3156 25.1748C35.7155 25.9228 35.7177 26.8423 36.3097 27.4812C36.6023
        27.8664 38.0253 28.9172 37.717 29.289C37.8957 29.6741 39.2249 30.2084 38.7647
        30.5824L38.7558 30.5735ZM63.7128 19.0525L60.8199 21.9378C60.0492 22.7058 59.0954
        23.2802 58.0633 23.5941L58.0119 23.6097L57.9918 23.6609C57.6545 24.547 57.1452
        25.3351 56.4728 26.0052L53.5776 28.8905C52.3155 30.1483 50.6356 30.8407 48.8485
        30.8407C47.0614 30.8407 45.3815 30.1483 44.1194 28.8927C41.5124 26.2946 41.5124
        22.0647 44.1194 19.4666L47.0145 16.5813C47.7896 15.8088 48.7167 15.2545 49.7666
        14.9317L49.818 14.9161L49.8381 14.8649C50.1732 13.9788 50.687 13.1885 51.3639
        12.5139L54.2568 9.62867C55.5189 8.37082 57.1988 7.67845 58.9859 7.67845C60.773
        7.67845 62.4551 8.37082 63.715 9.62867C64.975 10.8865 65.6719 12.5607 65.6697
        14.3417C65.6697 16.125 64.975 17.7991 63.7128 19.0547V19.0525Z\" fill=\"currentColor\"/>\n<path
        d=\"M26.9839 27.0806C26.7292 28.0668 26.6465 29.7477 25.3598 29.7966C25.2548
        30.3666 25.7552 30.5803 26.2132 30.3977C26.6644 30.1907 26.8789 30.5602 27.033
        30.9298C27.73 31.0322 28.7643 30.6983 28.8045 29.8723C27.7612 29.2735 27.4396
        28.1336 26.9861 27.0806H26.9839Z\" fill=\"currentColor\"/>\n</svg></div><h2
        class=\"heading-style-h2\">Build your app with LangChain</h2><p class=\"text-size-medium\">Build
        context-aware, reasoning applications with LangChain\u2019s flexible framework
        that leverages your company\u2019s data and APIs. Future-proof your application
        by making vendor optionality part of your LLM infrastructure design.</p></div><a
        href=\"/langchain\" class=\"button is-link w-inline-block\"><div>Learn more
        about LangChain</div><div class=\"link_icon w-embed\"><svg width=\"15\" height=\"14\"
        viewBox=\"0 0 15 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path
        fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M14.0788 1.96304L2.33407 13.7077L0.919861
        12.2935L12.6645 0.548828L14.0788 1.96304Z\" fill=\"CurrentColor\"/>\n<path
        fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12.5 2H1.5V0H14.5V13H12.5V2Z\"
        fill=\"CurrentColor\"/>\n</svg></div></a></div></div><div class=\"w-layout-grid
        product_component is-inverted\"><div id=\"w-node-_3077451b-9da5-5a12-4ade-d0cdb7f5d4ce-87f82a27\"
        class=\"product_text-wrapper\"><div class=\"product_text-content\"><div class=\"icon-embed-custom
        text-color-blue-500 w-embed\"><svg width=\"63\" height=\"33\" viewBox=\"0
        0 63 33\" fill=\"currentColor\" xmlns=\"http://www.w3.org/2000/svg\">\n<path
        fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M16.0556 0.580566H46.6516C55.3698
        0.580566 62.4621 7.69777 62.4621 16.4459C62.4621 25.194 55.3698 32.3112 46.6516
        32.3112H16.0556C7.33738 32.3112 0.245117 25.194 0.245117 16.4459C0.245117
        7.69777 7.33738 0.580566 16.0556 0.580566ZM30.103 25.1741C30.487 25.5781 31.0556
        25.5581 31.5593 25.4534L31.5643 25.4559C31.7981 25.2657 31.4658 25.0248 31.1484
        24.7948C30.9581 24.6569 30.7731 24.5228 30.7189 24.406C30.8946 24.1917 30.375
        23.7053 29.9704 23.3266C29.8006 23.1676 29.6511 23.0276 29.5818 22.9347C29.2939
        22.6213 29.1782 22.226 29.0618 21.8283C28.9846 21.5645 28.9071 21.2996 28.7788
        21.0569C27.9883 19.2215 27.083 17.401 25.8137 15.8474C24.9979 14.8148 24.0669
        13.8901 23.1356 12.965C22.5352 12.3687 21.9347 11.7722 21.3648 11.1466C20.7784
        10.5413 20.4255 9.79548 20.072 9.04847C19.7761 8.42309 19.4797 7.79685 19.0456
        7.25139C17.7314 5.30625 13.5818 4.77508 12.9733 7.52321C12.9758 7.60799 12.9484
        7.66286 12.8735 7.71772C12.5369 7.9646 12.2376 8.2439 11.9858 8.58306C11.3698
        9.44341 11.275 10.9023 12.0431 11.6753C12.0442 11.6587 12.0453 11.6422 12.0464
        11.6257C12.0721 11.2354 12.0961 10.8705 12.4047 10.5905C12.9982 11.1018 13.8985
        11.2838 14.5868 10.9023C15.4166 12.0926 15.681 13.5317 15.9462 14.9756C16.1671
        16.1784 16.3887 17.3847 16.9384 18.4534C16.9497 18.4723 16.9611 18.4912 16.9725
        18.5101C17.2955 19.048 17.6238 19.5946 18.0381 20.0643C18.1886 20.2976 18.4977
        20.5495 18.8062 20.8009C19.2132 21.1326 19.6194 21.4636 19.6591 21.7501C19.6609
        21.8748 19.6603 22.0012 19.6598 22.1283C19.6566 22.8808 19.6532 23.6601 20.1354
        24.2788C20.4022 24.82 19.7489 25.3636 19.2227 25.2963C18.9342 25.3363 18.619
        25.2602 18.306 25.1848C17.8778 25.0815 17.4537 24.9792 17.108 25.1766C17.011
        25.2816 16.8716 25.2852 16.7316 25.2889C16.5657 25.2933 16.3987 25.2977 16.3
        25.4708C16.2797 25.5223 16.2323 25.5804 16.183 25.6408C16.0748 25.7735 15.9575
        25.9173 16.098 26.0269C16.1106 26.0174 16.1231 26.0078 16.1356 25.9983C16.3484
        25.8358 16.5513 25.681 16.8386 25.7776C16.8004 25.9899 16.9375 26.0467 17.0745
        26.1036C17.0984 26.1135 17.1224 26.1234 17.1454 26.1342C17.1439 26.1835 17.1342
        26.2332 17.1245 26.2825C17.1015 26.4004 17.0789 26.516 17.1703 26.618C17.2137
        26.5738 17.2521 26.5243 17.2905 26.4746C17.3846 26.353 17.4791 26.2308 17.6491
        26.1865C18.023 26.6858 18.3996 26.4784 18.8721 26.2182C19.4051 25.9248 20.0601
        25.5641 20.9708 26.0743C20.6217 26.0569 20.3099 26.0993 20.0755 26.3885C20.0182
        26.4534 19.9683 26.5282 20.0705 26.613C20.6094 26.2639 20.8336 26.3893 21.0446
        26.5074C21.1969 26.5927 21.3423 26.6741 21.5942 26.5706C21.6538 26.5395 21.7133
        26.5074 21.7729 26.4752C22.1775 26.257 22.5877 26.0357 23.068 26.1117C22.7093
        26.2152 22.5816 26.4426 22.4423 26.6908C22.3734 26.8136 22.3017 26.9414 22.1977
        27.0619C22.1429 27.1167 22.1179 27.1815 22.1803 27.2738C22.9315 27.2114 23.2153
        27.0209 23.5988 26.7636C23.7818 26.6408 23.9875 26.5027 24.2775 26.3561C24.5981
        26.1587 24.9187 26.285 25.2293 26.4073C25.5664 26.54 25.8917 26.6681 26.1927
        26.3736C26.2878 26.284 26.4071 26.2829 26.5258 26.2818C26.569 26.2814 26.6122
        26.281 26.6541 26.2763C26.5604 25.7745 26.0319 25.7804 25.4955 25.7864C24.875
        25.7933 24.2438 25.8004 24.2626 25.022C24.8391 24.6282 24.8444 23.9449 24.8494
        23.299C24.8507 23.1431 24.8518 22.9893 24.8611 22.8424C25.2851 23.0788 25.7336
        23.2636 26.1794 23.4473C26.5987 23.62 27.0156 23.7917 27.4072 24.0045C27.8162
        24.6628 28.4546 25.5357 29.305 25.4783C29.3274 25.411 29.3474 25.3536 29.3723
        25.2863C29.4213 25.2949 29.4731 25.308 29.5257 25.3213C29.7489 25.3778 29.9879
        25.4384 30.103 25.1741ZM46.7702 17.6925C47.2625 18.1837 47.9304 18.4597 48.6267
        18.4597C49.323 18.4597 49.9909 18.1837 50.4832 17.6925C50.9756 17.2013 51.2523
        16.5351 51.2523 15.8404C51.2523 15.1458 50.9756 14.4795 50.4832 13.9883C49.9909
        13.4971 49.323 13.2212 48.6267 13.2212C48.3006 13.2212 47.9807 13.2817 47.6822
        13.3965L46.1773 11.1999L45.1285 11.9184L46.6412 14.1266C46.2297 14.6009 46.0011
        15.2089 46.0011 15.8404C46.0011 16.5351 46.2778 17.2013 46.7702 17.6925ZM42.0587
        10.5787C42.4271 10.7607 42.8332 10.8539 43.2443 10.8508C43.8053 10.8465 44.3501
        10.663 44.7989 10.3274C45.2478 9.99169 45.577 9.52143 45.7385 8.9855C45.9
        8.44957 45.8851 7.87615 45.6961 7.34925C45.5072 6.82235 45.154 6.36968 44.6884
        6.05757C44.3471 5.82883 43.9568 5.68323 43.5488 5.6325C43.1409 5.58176 42.7266
        5.62731 42.3396 5.76548C41.9525 5.90365 41.6033 6.13057 41.3202 6.42797C41.0371
        6.72537 40.8279 7.08494 40.7096 7.47773C40.5913 7.87051 40.567 8.28552 40.6389
        8.68935C40.7107 9.09317 40.8766 9.47453 41.1233 9.80269C41.3699 10.1309 41.6903
        10.3967 42.0587 10.5787ZM42.0587 25.7882C42.4271 25.9702 42.8332 26.0634 43.2443
        26.0602C43.8053 26.0559 44.3501 25.8725 44.7989 25.5368C45.2478 25.2011 45.577
        24.7309 45.7385 24.195C45.9 23.659 45.8851 23.0856 45.6961 22.5587C45.5072
        22.0318 45.154 21.5791 44.6884 21.267C44.3471 21.0383 43.9568 20.8927 43.5488
        20.842C43.1409 20.7912 42.7266 20.8368 42.3396 20.9749C41.9525 21.1131 41.6033
        21.34 41.3202 21.6374C41.0371 21.9348 40.8279 22.2944 40.7096 22.6872C40.5913
        23.08 40.567 23.495 40.6389 23.8988C40.7107 24.3026 40.8766 24.684 41.1233
        25.0122C41.3699 25.3403 41.6903 25.6061 42.0587 25.7882ZM44.4725 16.4916V15.1894H40.454C40.3529
        14.7946 40.1601 14.4289 39.8911 14.1216L41.4029 11.8819L40.3034 11.1526L38.7916
        13.3924C38.5145 13.2923 38.2224 13.2395 37.9277 13.2361C37.2333 13.2361 36.5675
        13.5105 36.0765 13.9989C35.5856 14.4874 35.3097 15.1498 35.3097 15.8405C35.3097
        16.5313 35.5856 17.1937 36.0765 17.6821C36.5675 18.1705 37.2333 18.4449 37.9277
        18.4449C38.2224 18.4416 38.5145 18.3888 38.7916 18.2887L40.3034 20.5284L41.3899
        19.7992L39.8911 17.5594C40.1601 17.2522 40.3529 16.8865 40.454 16.4916H44.4725Z\"
        fill=\"currentColor\"/>\n</svg></div><h2 class=\"heading-style-h2\">Run at
        scale with LangGraph\_Cloud</h2><p class=\"text-size-medium\">Deploy your
        LangGraph app with LangGraph Cloud for fault-tolerant scalability - including
        support for async background jobs, built-in persistence, and distributed task
        queues.</p></div><a href=\"/langgraph\" class=\"button is-link w-inline-block\"><div>Learn
        more about LangGraph</div><div class=\"link_icon w-embed\"><svg width=\"15\"
        height=\"14\" viewBox=\"0 0 15 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path
        fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M14.0788 1.96304L2.33407 13.7077L0.919861
        12.2935L12.6645 0.548828L14.0788 1.96304Z\" fill=\"CurrentColor\"/>\n<path
        fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12.5 2H1.5V0H14.5V13H12.5V2Z\"
        fill=\"CurrentColor\"/>\n</svg></div></a></div><div id=\"w-node-_3433c504-2da3-7d10-da26-74a85aa3cf13-87f82a27\"
        class=\"home_build-video-wrapper\"><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667c6d7284e58f4743a430e6_Langgraph%20UI-home-2.webp\"
        loading=\"lazy\" id=\"w-node-_756e1d1b-680d-0c4d-daa2-456dab276662-87f82a27\"
        sizes=\"100vw\" alt=\"\" srcset=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667c6d7284e58f4743a430e6_Langgraph%20UI-home-2-p-500.webp
        500w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667c6d7284e58f4743a430e6_Langgraph%20UI-home-2-p-800.webp
        800w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667c6d7284e58f4743a430e6_Langgraph%20UI-home-2-p-1080.webp
        1080w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667c6d7284e58f4743a430e6_Langgraph%20UI-home-2-p-1600.webp
        1600w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667c6d7284e58f4743a430e6_Langgraph%20UI-home-2.webp
        1768w\" class=\"product_langgraph-image-copy\"/></div></div><div class=\"w-layout-grid
        product_component is-home\"><div class=\"home_build-video-wrapper\"><div data-poster-url=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ce2a1ce257d3ea9e0be379_observe-animation-poster-00001.jpg\"
        data-video-urls=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ce2a1ce257d3ea9e0be379_observe-animation-transcode.mp4,https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ce2a1ce257d3ea9e0be379_observe-animation-transcode.webm\"
        data-autoplay=\"true\" data-loop=\"true\" data-wf-ignore=\"true\" class=\"product_bg-video
        w-background-video w-background-video-atom\"><video id=\"e48aa6f0-5db5-b5ce-1e0f-2150de4033cb-video\"
        autoplay=\"\" loop=\"\" style=\"background-image:url(&quot;https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ce2a1ce257d3ea9e0be379_observe-animation-poster-00001.jpg&quot;)\"
        muted=\"\" playsinline=\"\" data-wf-ignore=\"true\" data-object-fit=\"cover\"><source
        src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ce2a1ce257d3ea9e0be379_observe-animation-transcode.mp4\"
        data-wf-ignore=\"true\"/><source src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ce2a1ce257d3ea9e0be379_observe-animation-transcode.webm\"
        data-wf-ignore=\"true\"/></video></div></div><div class=\"product_text-wrapper\"><div
        class=\"product_text-content\"><div class=\"icon-embed-custom text-color-red-400
        w-embed\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"100%\" height=\"100%\"
        viewBox=\"0 0 77 39\" fill=\"none\" preserveAspectRatio=\"xMidYMid meet\"
        aria-hidden=\"true\" role=\"img\">\n<path d=\"M57.433 0H19.567C8.77775 0 0
        8.7477 0 19.5C0 30.2523 8.77775 39 19.567 39H57.433C68.2223 39 77 30.2523
        77 19.5C77 8.7477 68.2223 0 57.433 0ZM38.7563 30.5721C38.1321 30.7008 37.4297
        30.7242 36.9523 30.2269C36.7761 30.6306 36.3652 30.418 36.0483 30.3654C36.0189
        30.4492 35.9935 30.5194 35.9661 30.6013C34.9114 30.6715 34.1209 29.599 33.618
        28.7898C32.6201 28.2535 31.4872 27.924 30.4678 27.3604C30.4091 28.2886 30.6125
        29.4391 29.7262 30.0378C29.6812 31.822 32.4342 30.2484 32.6866 31.5783C32.491
        31.5997 32.2738 31.5471 32.1173 31.6973C31.3991 32.3934 30.5754 31.1707 29.7477
        31.6758C28.6363 32.2335 28.5248 32.6898 27.1492 32.8048C27.0729 32.6898 27.1042
        32.6138 27.1688 32.5435C27.5562 32.095 27.5836 31.5666 28.2449 31.3755C27.564
        31.2682 26.9946 31.6426 26.4213 31.939C25.6758 32.2432 25.6817 31.2526 24.535
        31.9917C24.4079 31.8883 24.4685 31.7947 24.5409 31.7148C24.8325 31.3599 25.216
        31.3072 25.6484 31.3267C23.5215 30.145 22.5196 32.7717 21.5374 31.4652C21.2419
        31.5432 21.1304 31.8084 20.9445 31.9956C20.7841 31.8201 20.9054 31.6075 20.9132
        31.4008C20.7214 31.3111 20.4788 31.2682 20.5356 30.9621C20.1599 30.8353 19.8977
        31.0576 19.6179 31.2682C19.3654 31.0732 19.7881 30.7885 19.8664 30.5858C20.0914
        30.1957 20.604 30.5058 20.8643 30.225C21.6039 29.8057 22.6351 30.4863 23.4804
        30.3712C24.132 30.4531 24.9381 29.7862 24.6114 29.1213C23.9148 28.234 24.038
        27.0738 24.0224 26.013C23.9363 25.3948 22.4472 24.607 22.0168 23.9401C21.4846
        23.3395 21.0697 22.6434 20.6549 21.959C19.158 19.071 19.6296 15.3601 17.7433
        12.6789C16.8902 13.1488 15.7788 12.9265 15.0431 12.2967C14.6459 12.6574 14.6283
        13.1293 14.597 13.6305C13.6441 12.6809 13.7634 10.8868 14.5246 9.82995C14.8357
        9.41265 15.2075 9.06945 15.6223 8.7672C15.7162 8.69895 15.7475 8.63265 15.7456
        8.52735C16.4989 5.14995 21.6372 5.80125 23.2612 8.1939C24.4392 9.66615 24.7953
        11.6142 26.1317 12.9811C27.9299 14.9428 29.9786 16.6705 31.6379 18.759C33.2071
        20.668 34.3283 22.9066 35.3067 25.1628C35.7058 25.9096 35.7098 26.83 36.3007
        27.4696C36.5922 27.8557 38.0167 28.9068 37.7075 29.2792C37.8856 29.6653 39.2162
        30.1977 38.7544 30.5721H38.7563ZM40.7189 14.4651L43.9181 11.1033C44.0922 10.92
        44.2879 10.7542 44.4953 10.6099L48.1817 8.05935C48.53 7.8195 49.0016 7.878
        49.2775 8.19975L50.8703 10.0464C51.1344 10.3525 51.1383 10.7991 50.8781 11.1091L48.759
        13.6383C48.6905 13.7202 48.6631 13.8274 48.6846 13.9308C48.6983 13.9971 48.7257
        14.0536 48.7648 14.1004L51.1931 17.0176L49.4595 18.8506L46.7494 15.7813C46.6986
        15.7248 46.6281 15.6838 46.5518 15.6624C46.4462 15.6351 46.3307 15.6526 46.2387
        15.7111L43.4152 17.4583C43.0865 17.6611 42.6541 17.6085 42.384 17.3296L40.7189
        15.6019C40.4137 15.2841 40.4117 14.7849 40.715 14.467L40.7189 14.4651ZM61.7123
        29.8759L60.869 30.5838C60.7105 30.7164 60.5109 30.7885 60.3055 30.7885C60.2565
        30.7885 60.2057 30.7847 60.1548 30.7749L59.4562 30.654C59.2508 30.6189 59.0629
        30.5097 58.9318 30.3498L52.2439 22.2885L54.1751 19.9953L61.7064 27.9006C61.8356
        28.0371 61.9178 28.2145 61.9393 28.4017L62.0176 29.1154C62.0489 29.404 61.9354
        29.6887 61.7123 29.8779V29.8759ZM63.352 16.7817C63.1896 16.9786 62.9274 17.0371
        62.6985 16.924L58.3644 14.8298C58.2548 14.7752 58.1276 14.7634 58.0102 14.7966C57.9418
        14.8141 57.8811 14.8453 57.8263 14.8882L57.0808 15.4713L44.5168 30.3946C44.3975
        30.5389 44.2292 30.6442 44.0433 30.6871L43.7831 30.7495C43.7166 30.7652 43.6481
        30.7729 43.5796 30.7729C43.3741 30.7729 43.1726 30.7008 43.0121 30.5662L41.8362
        29.5776C41.6092 29.3865 41.4957 29.0998 41.529 28.8073L41.5701 28.4524C41.5916
        28.2633 41.6757 28.0839 41.8088 27.9454L55.0048 13.9912L55.7209 13.0377C55.764
        12.9792 55.7874 12.909 55.7874 12.8349C55.7874 12.7452 55.7522 12.6613 55.6896
        12.5989L53.2946 10.1848C52.9796 9.867 52.962 9.35415 53.2516 9.01485L53.9794
        8.1666C54.271 7.8273 54.7836 7.7649 55.1456 8.0262L59.2469 10.9688C59.5365
        11.1774 59.7928 11.427 60.01 11.7117L63.3618 16.1109C63.5125 16.3098 63.5086
        16.5828 63.3501 16.7758L63.352 16.7817Z\" fill=\"currentColor\"/>\n</svg></div><h2
        class=\"heading-style-h2\">Manage LLM performance with\_LangSmith</h2><p class=\"text-size-medium\">Ship
        faster with LangSmith\u2019s debug, test, deploy, and monitoring workflows.
        Don\u2019t rely on \u201Cvibes\u201D \u2013 add engineering rigor to your
        LLM-development workflow, whether you\u2019re building with LangChain or not.</p></div><a
        href=\"/langsmith\" class=\"button is-link w-inline-block\"><div>Learn more
        about LangSmith</div><div class=\"link_icon w-embed\"><svg width=\"15\" height=\"14\"
        viewBox=\"0 0 15 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path
        fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M14.0788 1.96304L2.33407 13.7077L0.919861
        12.2935L12.6645 0.548828L14.0788 1.96304Z\" fill=\"CurrentColor\"/>\n<path
        fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12.5 2H1.5V0H14.5V13H12.5V2Z\"
        fill=\"CurrentColor\"/>\n</svg></div></a></div></div></div></div></div></section><div
        class=\"section_home-slider\"><section class=\"section_slider\"><div class=\"padding-global\"><div
        class=\"container-large\"><div class=\"happy-customers_heading\"><div id=\"w-node-de1f17ca-398b-87cf-f1fe-c1d40a2aa0f2-0a2aa0e8\"
        class=\"happy-customers_text-part\"><h3 class=\"heading-style-h2\">Hear from
        our happy customers</h3><p class=\"max-width-medium\">LangChain, LangGraph,
        and LangSmith help teams of all sizes, across all industries - from ambitious
        startups to established enterprises.</p></div></div><div class=\"happy-customers_slider-box\"><div
        class=\"swiper is-customers\"><div class=\"swiper-wrapper is-customers is-langgraph\"><div
        class=\"swiper-slide is-customers\"><div class=\"happy-customers_slide-text-part\"><div
        class=\"happy-customers_logotype-box\"><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65c5308aee06d9826765c897_Retool_logo%201.png\"
        loading=\"lazy\" width=\"77\" alt=\"\" class=\"happy-customers_logotype-img\"/></div><p
        class=\"text-size-regular is-opacity-80\">\u201CLangSmith helped us improve
        the accuracy and performance of Retool\u2019s fine-tuned models. Not only
        did we deliver a better product by iterating with LangSmith, but we\u2019re
        shipping new AI features to our users in a fraction of the time it would have
        taken without it.\u201D</p></div><div class=\"happy-customers_slide-author-part\"><img
        src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65c5308abdd2dbbdde5a94a1_Jamie%20Cuffe.png\"
        loading=\"lazy\" alt=\"\" class=\"happy-customers_slide-author-img\"/><div
        class=\"happy-customers_slide-author-text\"><div class=\"heading-style-h6\">Jamie
        Cuffe</div><div class=\"text-size-regular is-opacity-80\">Head of Self-Serve
        and New Products</div></div></div></div><div class=\"swiper-slide is-customers\"><div
        class=\"happy-customers_slide-text-part\"><div class=\"happy-customers_logotype-box\"><img
        src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65c5308a04d37cf7d3eb1341_Rakuten_Global_Brand_Logo.png\"
        loading=\"lazy\" alt=\"\" class=\"happy-customers_logotype-img\"/></div><p
        class=\"text-size-regular is-opacity-80\">\u201CBy combining the benefits
        of LangSmith and standing on the shoulders of a gigantic open-source community,
        we\u2019re able to identify the right approaches of using LLMs in an enterprise-setting
        faster.\u201D</p></div><div class=\"happy-customers_slide-author-part\"><img
        src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65c5308a8b6137d44c621cb4_Yusuke%20Kaji.png\"
        loading=\"lazy\" alt=\"\" class=\"happy-customers_slide-author-img\"/><div
        class=\"happy-customers_slide-author-text\"><div class=\"heading-style-h6\">Yusuke
        Kaji</div><div class=\"text-size-regular is-opacity-80\">General Manager of
        AI</div></div></div></div><div class=\"swiper-slide is-customers\"><div class=\"happy-customers_slide-text-part\"><div
        class=\"happy-customers_logotype-box is-no-padding\"><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65c5308aea1371b447cc4af9_elastic-ar21.png\"
        loading=\"lazy\" alt=\"\" class=\"happy-customers_logotype-img is--bigger\"/></div><p
        class=\"text-size-regular is-opacity-80\">\u201CWorking with LangChain and
        LangSmith on the Elastic AI Assistant had a significant positive impact on
        the overall pace and quality of the development and shipping experience. We
        couldn\u2019t have achieved \_the product experience delivered to our customers
        without LangChain, and we couldn\u2019t have done it at the same pace without
        LangSmith.\u201D</p></div><div class=\"happy-customers_slide-author-part\"><img
        src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65c5308a4095d5a871de7479_James%20Spiteri.png\"
        loading=\"lazy\" alt=\"\" class=\"happy-customers_slide-author-img\"/><div
        class=\"happy-customers_slide-author-text\"><div class=\"heading-style-h6\">James
        Spiteri</div><div class=\"text-size-regular is-opacity-80\">Director of Security
        Products</div></div></div></div><div class=\"swiper-slide is-customers\"><div
        class=\"happy-customers_slide-text-part\"><div class=\"happy-customers_logotype-box\"><img
        src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65c530539f4824b828357352_Logo_de_Fintual%201.png\"
        loading=\"lazy\" width=\"88\" alt=\"\" class=\"happy-customers_logotype-img\"/></div><p
        class=\"text-size-regular is-opacity-80\">\u201CAs soon as we heard about
        LangSmith, we moved our entire development stack onto it. We could have built
        evaluation, testing and monitoring tools in house, but with LangSmith it took
        us 10x less time to get a 1000x better tool.\u201D</p></div><div class=\"happy-customers_slide-author-part\"><img
        src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65c53058acbff86f4c2dcee2_jose%20pena.png\"
        loading=\"lazy\" alt=\"\" class=\"happy-customers_slide-author-img\"/><div
        class=\"happy-customers_slide-author-text\"><div class=\"heading-style-h6\">Jose
        Pe\xF1a</div><div class=\"text-size-regular is-opacity-80\">Senior Manager</div></div></div></div></div></div></div><div
        id=\"w-node-de1f17ca-398b-87cf-f1fe-c1d40a2aa0ec-0a2aa0e8\" class=\"happy-customers_navigation-part\"><div
        class=\"happy-customers_navigation-group\"><div class=\"single-arrow is-prev\"><div
        class=\"icon-embed-custom is--arrow w-embed\"><svg xmlns=\"http://www.w3.org/2000/svg\"
        width=\"100%\" height=\"100%\" viewBox=\"0 0 19 19\" fill=\"none\" preserveAspectRatio=\"xMidYMid
        meet\" aria-hidden=\"true\" role=\"img\">\n<path d=\"M18.3946 9.42475L1.7851
        9.42475\" stroke=\"currentColor\" stroke-width=\"2\"/>\n<path d=\"M10 18L1.51472
        9.51472L10 1.02944\" stroke=\"currentColor\" stroke-width=\"2\"/>\n</svg></div></div><div
        class=\"single-arrow is-next\"><div class=\"icon-embed-custom is--arrow w-embed\"><svg
        xmlns=\"http://www.w3.org/2000/svg\" width=\"100%\" height=\"100%\" viewBox=\"0
        0 19 19\" fill=\"none\" preserveAspectRatio=\"xMidYMid meet\" aria-hidden=\"true\"
        role=\"img\">\n<path d=\"M0.605412 9.42475L17.2149 9.42475\" stroke=\"currentColor\"
        stroke-width=\"2\"/>\n<path d=\"M9 18L17.4853 9.51472L9 1.02944\" stroke=\"currentColor\"
        stroke-width=\"2\"/>\n</svg></div></div></div></div></div><div class=\"hide
        w-embed w-script\"><link\n  rel=\"stylesheet\"\n  href=\"https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css\"\n/>\n<script
        src=\"https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js\"></script>\n<script>\nvar
        Webflow = Webflow || [];\nWebflow.push(function () {\n  // DOMready has fired\n
        \ // May now use jQuery and Webflow api\n\n\nconst teamSlider = new Swiper(\".swiper.is-customers\",
        {\n  // Parameters\n  loop: true,\n  speed: 700,\n\tloopAdditionalSlides:
        5,\n  grabCursor: true,\n  slidesPerView: 1.2,\n  spaceBetween: 24,\n  allowTouchMove:
        true,\n\n  // Navigation arrows\n  navigation: {\n    nextEl: \".single-arrow.is-next\",\n
        \   prevEl: \".single-arrow.is-prev\"\n  },\n  autoplay: {\n    delay: 5000,\n
        \   disableOnInteraction: false,\n    pauseOnMouseEnter: true,\n  },\n\n  //
        Responsive breakpoints\n  breakpoints: {\n    // when window width is >= 480px\n
        \   860: {\n      slidesPerView: 2,\n      spaceBetween: 30\n    },\n    //
        when window width is >= 1200px\n    1200: {\n      slidesPerView: 3,\n      spaceBetween:
        40\n    }\n  }\n});\n\n});\n</script>\n\n<style>\n @media screen and (max-width:991px)
        {.swiper-slide {\nheight: auto;\n}}\n</style></div></div></section></div><section
        class=\"section_reference background-color-green400\"><div class=\"padding-global\"><div
        class=\"container-xlarge\"><div data-w-id=\"00671cb6-faa2-aff9-f3ca-4d15160aa934\"
        class=\"w-layout-grid reference_component is-horizontal-tablet\"><div class=\"max-width-medium-plus
        is-padding-top\"><h2 class=\"heading-style-h2 margin-bottom margin-xsmall\">The
        reference architecture enterprises adopt for success.</h2><p class=\"text-size-medium
        max-width-small\">LangChain\u2019s suite of products can be used independently
        or stacked together for multiplicative impact \u2013 guiding you through building,
        running, and managing your LLM apps.</p></div><div class=\"architecture-image_wrapper\"><img
        src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/6695b116b0b60c78fd4ef462_15.07.24%20-Updated%20stack%20diagram%20-%20lightfor%20website-3.webp\"
        loading=\"lazy\" height=\"Auto\" alt=\"\" width=\"71\" class=\"fullwidth-image
        is-desktop\"/><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667d392696fc0bc3e17a6d04_New%20LC%20stack%20-%20light-2.webp\"
        loading=\"lazy\" width=\"623\" sizes=\"(max-width: 479px) 93vw, (max-width:
        767px) 96vw, (max-width: 991px) 45vw, 100vw\" alt=\"\" srcset=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667d392696fc0bc3e17a6d04_New%20LC%20stack%20-%20light-2-p-500.webp
        500w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667d392696fc0bc3e17a6d04_New%20LC%20stack%20-%20light-2.webp
        833w\" class=\"fullwidth-image is-mobile\"/></div></div></div></div></section><section
        class=\"section_community\"><div class=\"padding-global\"><div class=\"container-xlarge\"><div
        data-w-id=\"43123052-e7a7-8a89-24f2-e32f8cae37e1\" class=\"w-layout-grid community_component
        is-inverted\"><div id=\"w-node-_53103223-f4d4-f3e9-d36a-ce593a1a793c-87f82a27\"><div
        class=\"community_bubbles-row\"><div class=\"community_bubble\"><div class=\"heading-style-decorative-2
        is-numeric\">15M+</div><div class=\"heading-style-h6\">Monthly Downloads</div></div><div
        class=\"community_bubble is-right\"><div class=\"heading-style-decorative-2
        is-numeric\">100K+</div><div class=\"heading-style-h6\">Apps Powered</div></div></div><div
        class=\"community_bubbles-row is-bottom\"><div class=\"community_bubble\"><div
        class=\"heading-style-decorative-2 is-numeric\">75K+</div><div class=\"heading-style-h6\">GitHub
        Stars</div></div><div class=\"community_bubble is-right\"><div class=\"heading-style-decorative-2
        is-numeric\">3K+</div><div class=\"heading-style-h6\">Contributors</div></div></div></div><div
        class=\"product_text-wrapper\"><div class=\"product_text-content\"><h2 class=\"heading-style-h2\">The
        biggest developer community in GenAI</h2><p class=\"text-size-medium\">Learn
        alongside the 1M+ developers who are pushing the industry forward.</p></div><a
        href=\"/langchain\" class=\"button is-link w-inline-block\"><div>Explore LangChain</div><div
        class=\"link_icon w-embed\"><svg width=\"15\" height=\"14\" viewBox=\"0 0
        15 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path fill-rule=\"evenodd\"
        clip-rule=\"evenodd\" d=\"M14.0788 1.96304L2.33407 13.7077L0.919861 12.2935L12.6645
        0.548828L14.0788 1.96304Z\" fill=\"CurrentColor\"/>\n<path fill-rule=\"evenodd\"
        clip-rule=\"evenodd\" d=\"M12.5 2H1.5V0H14.5V13H12.5V2Z\" fill=\"CurrentColor\"/>\n</svg></div></a></div></div></div></div></section><section
        class=\"section_home-cta\"><div class=\"padding-global is-wide\"><div class=\"container-xlarge\"><div
        class=\"cta-block_bg-content\"><div data-w-id=\"6e007325-e41b-6cec-6218-6942fd9199ed\"
        style=\"opacity:0\" class=\"cta-block_text-part\"><div class=\"product_text-content\"><h2
        class=\"heading-style-h2 text-align-center\">Get started with the LangSmith
        platform today</h2></div><div class=\"cta-block_buttons-wrap\"><a href=\"/contact-sales\"
        class=\"button w-button\">Get a demo</a><a href=\"https://smith.langchain.com/\"
        target=\"_blank\" class=\"button is-alternate is-fill-green w-button\">Sign
        up for free</a></div></div><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ccf12801bc39bf912a58f3_Home%20C.webp\"
        loading=\"lazy\" width=\"920\" sizes=\"(max-width: 479px) 95vw, (max-width:
        767px) 97vw, (max-width: 991px) 98vw, (max-width: 1439px) 95vw, (max-width:
        1919px) 96vw, 1808px\" alt=\"\" srcset=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ccf12801bc39bf912a58f3_Home%20C-p-500.webp
        500w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ccf12801bc39bf912a58f3_Home%20C-p-800.webp
        800w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ccf12801bc39bf912a58f3_Home%20C-p-1080.webp
        1080w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ccf12801bc39bf912a58f3_Home%20C-p-1600.webp
        1600w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ccf12801bc39bf912a58f3_Home%20C.webp
        1840w\" class=\"home_cta-bg-image\"/></div></div></div></section><header id=\"blog-header-1\"
        class=\"section_home-inspiration\"><div class=\"padding-global is-inspo\"><div
        class=\"container-xlarge\"><div data-w-id=\"f49f52e7-0f65-8bf9-79c6-b74cb4e015a5\"
        class=\"inspo_component\"><div class=\"w-layout-grid layout_component is-bigger-gap\"><div
        class=\"layout_content-col\"><p class=\"margin-bottom margin-medium ishomepage\">Teams
        building with LangChain are driving operational efficiency, increasing discovery
        &amp; personalization, and delivering premium products that generate revenue.</p><a
        href=\"/use-cases\" class=\"button is-link w-inline-block\"><div>Discover
        Use Cases</div><div class=\"link_icon w-embed\"><svg width=\"15\" height=\"14\"
        viewBox=\"0 0 15 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path
        fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M14.0788 1.96304L2.33407 13.7077L0.919861
        12.2935L12.6645 0.548828L14.0788 1.96304Z\" fill=\"CurrentColor\"/>\n<path
        fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12.5 2H1.5V0H14.5V13H12.5V2Z\"
        fill=\"CurrentColor\"/>\n</svg></div></a></div><div class=\"layout_content-right\"><h2>Get
        inspired by companies who have done it.</h2></div></div><div class=\"use-case_list-wrapper\"><div
        class=\"w-layout-grid use-case_list\"><a data-w-id=\"f49f52e7-0f65-8bf9-79c6-b74cb4e015a8\"
        style=\"opacity:0\" href=\"https://blog.langchain.dev/ally-financial-collaborates-with-langchain-to-deliver-critical-coding-module-to-mask-personal-identifying-information-in-a-compliant-and-safe-manner/\"
        target=\"_blank\" class=\"use-case_item is-alternate w-inline-block\"><div
        class=\"use-case_image-link\"><div class=\"use-case_image-wrapper\"><div class=\"use-case_image
        is-alternate\"><img alt=\"\" loading=\"lazy\" width=\"88.5\" src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65bcd7ee85507bdf350399c3_Ally_Financial%201.svg\"/></div></div></div><div
        class=\"use-case_link-wrap\"><div class=\"use-case_pill\">Financial Services</div><div><div
        class=\"icon-embed-medium is-use-case w-embed\"><svg xmlns=\"http://www.w3.org/2000/svg\"
        width=\"100%\" height=\"100%\" viewBox=\"0 0 42 42\" fill=\"none\" preserveAspectRatio=\"xMidYMid
        meet\" aria-hidden=\"true\" role=\"img\">\n<path d=\"M1.42187 41.0005L40.5708
        1.85156\" stroke=\"currentColor\" stroke-width=\"2\"/>\n<path d=\"M1 0.999512L41
        0.999512V40.9995\" stroke=\"currentColor\" stroke-width=\"2\"/>\n</svg></div></div></div></a><a
        data-w-id=\"0b88affc-7742-f857-71d8-************\" style=\"opacity:0\" href=\"https://blog.langchain.dev/llms-accelerate-adyens-support-team-through-smart-ticket-routing-and-support-agent-copilot/\"
        target=\"_blank\" class=\"use-case_item is-alternate w-inline-block\"><div
        class=\"use-case_image-link\"><div class=\"use-case_image-wrapper\"><div class=\"use-case_image
        is-alternate\"><img width=\"136\" loading=\"lazy\" src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65bcd8b3ae4dc901daa3037a_Adyen_Corporate_Logo%201.svg\"
        alt=\"\"/></div></div></div><div class=\"use-case_link-wrap\"><div class=\"use-case_pill\">FinTech</div><div><div
        class=\"icon-embed-medium is-use-case w-embed\"><svg xmlns=\"http://www.w3.org/2000/svg\"
        width=\"100%\" height=\"100%\" viewBox=\"0 0 42 42\" fill=\"none\" preserveAspectRatio=\"xMidYMid
        meet\" aria-hidden=\"true\" role=\"img\">\n<path d=\"M1.42187 41.0005L40.5708
        1.85156\" stroke=\"currentColor\" stroke-width=\"2\"/>\n<path d=\"M1 0.999512L41
        0.999512V40.9995\" stroke=\"currentColor\" stroke-width=\"2\"/>\n</svg></div></div></div></a><a
        data-w-id=\"a310b82f-3112-9979-5481-ecce5be7c63f\" style=\"opacity:0\" href=\"https://blog.langchain.dev/langchain-partners-with-elastic-to-launch-the-elastic-ai-assistant/\"
        target=\"_blank\" class=\"use-case_item is-alternate w-inline-block\"><div
        class=\"use-case_image-link\"><div class=\"use-case_image-wrapper\"><div class=\"use-case_image
        is-alternate\"><img width=\"280\" loading=\"lazy\" src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65c534b3fa387379c0f4ebff_elastic-ar21%20(1).png\"
        alt=\"\" class=\"inspo_image is-elastic\"/></div></div></div><div class=\"use-case_link-wrap\"><div
        class=\"use-case_pill\">Technology</div><div><div class=\"icon-embed-medium
        is-use-case w-embed\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"100%\"
        height=\"100%\" viewBox=\"0 0 42 42\" fill=\"none\" preserveAspectRatio=\"xMidYMid
        meet\" aria-hidden=\"true\" role=\"img\">\n<path d=\"M1.42187 41.0005L40.5708
        1.85156\" stroke=\"currentColor\" stroke-width=\"2\"/>\n<path d=\"M1 0.999512L41
        0.999512V40.9995\" stroke=\"currentColor\" stroke-width=\"2\"/>\n</svg></div></div></div></a></div></div></div></div></div></header><section
        class=\"section_home-langsmith\"><div class=\"padding-global\"><div class=\"container-xlarge\"><div
        class=\"w-layout-grid reference_component\"><div id=\"w-node-_87e307f2-a5bc-2c2f-8881-41eb8462a4d7-87f82a27\"
        class=\"flex-left\"><h2 class=\"heading-style-h2 margin-medium margin-bottom
        is-homepage\">LangSmith is the enterprise\_DevOps platform\_built for LLMs.</h2><a
        href=\"/langsmith\" target=\"_blank\" class=\"button is-link w-inline-block\"><div>Explore
        LangSmith</div><div class=\"link_icon w-embed\"><svg width=\"15\" height=\"14\"
        viewBox=\"0 0 15 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path
        fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M14.0788 1.96304L2.33407 13.7077L0.919861
        12.2935L12.6645 0.548828L14.0788 1.96304Z\" fill=\"CurrentColor\"/>\n<path
        fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12.5 2H1.5V0H14.5V13H12.5V2Z\"
        fill=\"CurrentColor\"/>\n</svg></div></a></div><div><div class=\"home_langsmith-single\"><div
        class=\"home_langsmith-dot w-embed\"><svg xmlns=\"http://www.w3.org/2000/svg\"
        width=\"100%\" height=\"100%\" viewBox=\"0 0 8 8\" fill=\"none\" preserveAspectRatio=\"xMidYMid
        meet\" aria-hidden=\"true\" role=\"img\">\n<circle cx=\"4\" cy=\"4\" r=\"4\"
        fill=\"currentColor\"/>\n</svg></div><div class=\"heading-style-h5\">Gain
        visibility to make trade offs between cost, latency, and quality.</div></div><div
        class=\"home_langsmith-single\"><div class=\"home_langsmith-dot w-embed\"><svg
        xmlns=\"http://www.w3.org/2000/svg\" width=\"100%\" height=\"100%\" viewBox=\"0
        0 8 8\" fill=\"none\" preserveAspectRatio=\"xMidYMid meet\" aria-hidden=\"true\"
        role=\"img\">\n<circle cx=\"4\" cy=\"4\" r=\"4\" fill=\"currentColor\"/>\n</svg></div><div
        class=\"heading-style-h5\">Increase developer productivity.</div></div><div
        class=\"home_langsmith-single\"><div class=\"home_langsmith-dot w-embed\"><svg
        xmlns=\"http://www.w3.org/2000/svg\" width=\"100%\" height=\"100%\" viewBox=\"0
        0 8 8\" fill=\"none\" preserveAspectRatio=\"xMidYMid meet\" aria-hidden=\"true\"
        role=\"img\">\n<circle cx=\"4\" cy=\"4\" r=\"4\" fill=\"currentColor\"/>\n</svg></div><div
        class=\"heading-style-h5\">Eliminate manual, error-prone testing.</div></div><div
        class=\"home_langsmith-single\"><div class=\"home_langsmith-dot w-embed\"><svg
        xmlns=\"http://www.w3.org/2000/svg\" width=\"100%\" height=\"100%\" viewBox=\"0
        0 8 8\" fill=\"none\" preserveAspectRatio=\"xMidYMid meet\" aria-hidden=\"true\"
        role=\"img\">\n<circle cx=\"4\" cy=\"4\" r=\"4\" fill=\"currentColor\"/>\n</svg></div><div
        class=\"heading-style-h5\">Reduce hallucinations and improve reliability.</div></div><div
        class=\"home_langsmith-single is-last\"><div class=\"home_langsmith-dot w-embed\"><svg
        xmlns=\"http://www.w3.org/2000/svg\" width=\"100%\" height=\"100%\" viewBox=\"0
        0 8 8\" fill=\"none\" preserveAspectRatio=\"xMidYMid meet\" aria-hidden=\"true\"
        role=\"img\">\n<circle cx=\"4\" cy=\"4\" r=\"4\" fill=\"currentColor\"/>\n</svg></div><div
        class=\"heading-style-h5\">Enterprise deployment options to keep data secure.</div></div></div></div></div></div></section><section
        class=\"section_bottom-cta\"><div class=\"padding-global is-wide\"><div class=\"container-xlarge\"><div
        class=\"padding-section-main is-join-us\"><div class=\"w-layout-grid join-us_component\"><div
        class=\"layout_content-right max-width-medium\"><h2 class=\"margin-small margin-bottom\">Ready
        to start shipping \Lreliable GenAI apps faster?</h2><p class=\"text-size-medium\">Get
        started with LangChain, LangGraph, and LangSmith to enhance your LLM app development,
        from prototype to production.</p><div class=\"join-us_buttons-wrapper is-centered\"><a
        href=\"/contact-sales\" class=\"button w-button\">Get a demo</a><a href=\"https://smith.langchain.com/\"
        target=\"_blank\" class=\"button is-secondary w-button\">Sign up for free</a></div></div></div></div></div></div></section></main><footer
        class=\"footer_component\"><div class=\"padding-global\"><div class=\"footer_top-component\"><div
        class=\"w-layout-grid footer_top-wrapper\"><div class=\"w-layout-grid footer_left-wrapper\"><div
        class=\"footer_link-list\"><div class=\"margin-bottom margin-small\"><div
        class=\"heading-style-h6 is-footer\">Products</div></div><div id=\"w-node-ed59fd8f-4548-9b12-643e-b1816711f5bf-6711f5b5\"
        class=\"footer-link_column\"><a href=\"/langchain\" class=\"footer_link\">LangChain</a><a
        href=\"/langsmith\" class=\"footer_link\">LangSmith</a><a href=\"/langgraph\"
        class=\"footer_link\">LangGraph</a><a href=\"/agents\" class=\"footer_link\">Agents</a><a
        href=\"/evaluation\" class=\"footer_link\">Evaluation</a><a href=\"/retrieval\"
        class=\"footer_link\">Retrieval</a></div></div><div class=\"footer_link-list\"><div
        class=\"margin-bottom margin-small\"><div class=\"heading-style-h6 is-footer\">Resources</div></div><div
        class=\"footer-link_column is-resources\"><a href=\"https://python.langchain.com/\"
        target=\"_blank\" class=\"footer_link\">Python Docs</a><a href=\"https://js.langchain.com/docs/get_started/introduction/\"
        target=\"_blank\" class=\"footer_link\">JS/TS Docs</a><a href=\"https://github.com/langchain-ai\"
        target=\"_blank\" class=\"footer_link link\">GitHub</a><a href=\"https://python.langchain.com/v0.2/docs/integrations/platforms/\"
        target=\"_blank\" class=\"footer_link\">Integrations</a><a href=\"https://changelog.langchain.com/\"
        target=\"_blank\" class=\"footer_link\">Changelog</a><a href=\"https://trust.langchain.com/\"
        target=\"_blank\" class=\"footer_link\">LangSmith Trust Portal</a></div></div><div
        class=\"footer_link-list\"><div class=\"margin-bottom margin-small\"><div
        class=\"heading-style-h6 is-footer\">Company</div></div><div class=\"footer-link_column\"><a
        href=\"/about\" class=\"footer_link\">About</a><a href=\"https://blog.langchain.dev/\"
        target=\"_blank\" class=\"footer_link\">Blog</a><a href=\"https://twitter.com/LangChainAI\"
        target=\"_blank\" class=\"footer_link\">Twitter</a><a href=\"https://www.linkedin.com/company/langchain/\"
        target=\"_blank\" class=\"footer_link\">LinkedIn</a><a href=\"https://www.youtube.com/@LangChain\"
        target=\"_blank\" class=\"footer_link\">YouTube</a><a href=\"/join-community\"
        class=\"footer_link\">Community</a><a href=\"https://drive.google.com/drive/folders/17xybjzmVBdsQA-VxouuGLxF6bDsHDe80?usp=sharing\"
        target=\"_blank\" class=\"footer_link\">Marketing Assets</a></div></div></div><div
        id=\"w-node-ed59fd8f-4548-9b12-643e-b1816711f5ee-6711f5b5\" class=\"footer_right-wrapper\"><div
        class=\"margin-bottom margin-xsmall\"><div class=\"footer__form-title\"><div
        class=\"heading-style-h6\">Sign up for our newsletter to stay up to date</div></div></div><div
        class=\"footer_form-block w-form\"><form id=\"wf-form-footer_form\" name=\"wf-form-footer_form\"
        data-name=\"footer_form\" action=\"https://langchain.us11.list-manage.com/subscribe/post?u=4ad47fb6a8889d6654064ff1b&amp;amp;id=6bc1ef8ac4&amp;amp;f_id=0072b7e0f0\"
        method=\"post\" class=\"footer_form\" data-wf-page-id=\"660189f45f9033dd87f82a27\"
        data-wf-element-id=\"ed59fd8f-4548-9b12-643e-b1816711f5f5\"><input class=\"form_input
        w-input\" maxlength=\"256\" name=\"EMAIL\" data-name=\"EMAIL\" placeholder=\"Your
        email...\" type=\"email\" id=\"EMAIL\" required=\"\"/><input type=\"submit\"
        data-wait=\"Please wait...\" aria-label=\"Subscribe\" class=\"news_button
        w-button\" value=\"\"/></form><div class=\"form_success is-footer w-form-done\"><div>Thank
        you! Your submission has been received!</div></div><div class=\"form_error
        is-news w-form-fail\"><div>Oops! Something went wrong while submitting the
        form.</div></div></div></div></div></div><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65c6a38f9c53ec71f5fc73de_langchain-word.svg\"
        loading=\"lazy\" alt=\"\" class=\"footer_logo\"/></div><div class=\"padding-global
        is-bottom-footer\"><div class=\"footer_bottom-component\"><div class=\"padding-vertical\"><div
        class=\"footer_bottom\"><a href=\"https://status.smith.langchain.com/\" target=\"_blank\"
        class=\"footer_link is-status w-inline-block\"><div class=\"status-dot\"></div><div>All
        systems <span class=\"system-status\">operational</span></div></a><a href=\"/privacy-policy\"
        class=\"footer_link\">Privacy Policy</a><a href=\"/terms-of-service\" class=\"footer_link\">Terms
        of Service</a><div class=\"hide w-embed w-script\"><script type=\"text/javascript\"
        src=\"https://cdn.statuspage.io/se-v2.js\"></script>\n<script>\n  var sp =
        new StatusPage.page({ page : 'nl3597xjtlm2' });\n  sp.summary({\n    success
        : function(data) {\n      // Get the status value from the first component\n
        \     var status = data.components[0].status;\n      \n      // Log the status
        to the console (optional, you can remove this line)\n      console.log(status);\n
        \     \n      // Find the span with the class '.system-status' and set its
        content\n      var statusSpan = document.querySelector('.system-status');\n
        \     if(statusSpan) {\n        statusSpan.textContent = status; // or use
        innerHTML if you need to include HTML\n      }\n    }\n  });\n</script></div></div></div></div></div></footer></div><script
        src=\"https://d3e54v103j8qbb.cloudfront.net/js/jquery-3.5.1.min.dc5e7f18c8.js?site=65b8cd72835ceeacd4449a53\"
        type=\"text/javascript\" integrity=\"sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0=\"
        crossorigin=\"anonymous\"></script><script src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/js/langchain-tonik.1edf28489.js\"
        type=\"text/javascript\"></script><script>\n\nvar Webflow = Webflow || [];\nWebflow.push(function
        () {\n  // DOMready has fired\n  // May now use jQuery and Webflow api\ndocument.querySelector('.w-nav-button').addEventListener('click',
        function() {\n    var logo = document.querySelector('.navbar_logo-svg');\n
        \   \n    // Toggle 'open' class for both nav and logo\n    logo.classList.toggle('is-light');\n});\n});\n\n</script>\n\n<script>\n//
        Debounce function to limit the rate at which a function is executed\nfunction
        debounce(func, wait) {\n    let timeout;\n    return function executedFunction(...args)
        {\n        const later = () => {\n            clearTimeout(timeout);\n            func(...args);\n
        \       };\n        clearTimeout(timeout);\n        timeout = setTimeout(later,
        wait);\n    };\n}\n\n// Function to update video sources\nfunction updateVideoSources()
        {\n    const videos = document.querySelectorAll('video'); // Select all video
        elements\n    videos.forEach(video => {\n        const sources = video.querySelectorAll('source[data-src]');
        // Select all source elements with a \"data-src\" attribute within each video\n
        \       if (window.innerWidth > 990) {\n            sources.forEach(source
        => {\n                source.src = source.getAttribute('data-src'); // If
        screen width > 990, set src to data-src\n            });\n        } else {\n
        \           sources.forEach(source => {\n                source.src = '#';
        \n            });\n        }\n        video.load(); // Important: load the
        new source(s) into the video\n    });\n}\n\n// Event listener for DOMContentLoaded
        to run once on start\ndocument.addEventListener('DOMContentLoaded', function()
        {\n    updateVideoSources();\n});\n\n// Event listener for resize with debounce\nwindow.addEventListener('resize',
        debounce(updateVideoSources, 250)); // Debounce resize event\n\n</script>\n\n<script
        type=\"text/javascript\">\n\t_linkedin_partner_id = \"5973154\";\n\twindow._linkedin_data_partner_ids
        = window._linkedin_data_partner_ids || [];\n\twindow._linkedin_data_partner_ids.push(_linkedin_partner_id);\n</script>\n\n<script
        type=\"text/javascript\">\n\t(function(l) {\n\t\tif (!l){window.lintrk = function(a,b){window.lintrk.q.push([a,b])};\t\n\t\twindow.lintrk.q=[]}\n\tvar
        s = document.getElementsByTagName(\"script\")[0];\n\tvar b = document.createElement(\"script\");\n\tb.type
        = \"text/javascript\";b.async = true;\n\tb.src = \"https://snap.licdn.com/li.lms-analytics/insight.min.js\";\n\ts.parentNode.insertBefore(b,
        s);})(window.lintrk);\n</script>\n\n<noscript>\n\t<img height=\"1\" width=\"1\"
        style=\"display:none;\" alt=\"\" src=\"https://px.ads.linkedin.com/collect/?pid=5973154&fmt=gif\"
        />\n</noscript></body></html>"
    headers:
      Accept-Ranges:
      - bytes
      Age:
      - '71160'
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Length:
      - '27719'
      Content-Type:
      - text/html
      Date:
      - Mon, 16 Sep 2024 12:21:43 GMT
      Vary:
      - x-wf-forwarded-proto, Accept-Encoding
      X-Cache:
      - HIT
      X-Cache-Hits:
      - '1'
      X-Cluster-Name:
      - us-west-2-prod-hosting-red
      X-Served-By:
      - cache-bfi-kbfi7400076-BFI
      X-Timer:
      - S1726489304.577643,VS0,VE1
      content-security-policy:
      - frame-ancestors 'self'
      x-frame-options:
      - SAMEORIGIN
      x-lambda-id:
      - cfdb409d-7e69-4562-bc4d-7022736a58f9
    status:
      code: 200
      message: OK
- request:
    body: '{"max_tokens": 1024, "messages": [{"role": "user", "content": "You are
      doing web research on behalf of a user. You are trying to find out this information:\n\n<info>\n{\n  \"type\":
      \"object\",\n  \"properties\": {\n    \"founder\": {\n      \"type\": \"string\",\n      \"description\":
      \"The name of the company founder.\"\n    },\n    \"websiteUrl\": {\n      \"type\":
      \"string\",\n      \"description\": \"Website URL of the company, e.g.: https://openai.com/,
      or https://microsoft.com\"\n    },\n    \"products_sold\": {\n      \"type\":
      \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      },\n      \"description\":
      \"A list of products sold by the company.\"\n    }\n  },\n  \"required\": [\n    \"founder\",\n    \"websiteUrl\",\n    \"products_sold\"\n  ]\n}\n</info>\n\nYou
      just scraped the following website: https://www.langchain.com/\n\nBased on the
      website content below, jot down some notes about the website.\n\n<Website content>\n<!DOCTYPE
      html><!-- Last Published: Sun Sep 15 2024 15:59:13 GMT+0000 (Coordinated Universal
      Time) --><html data-wf-domain=\"www.langchain.com\" data-wf-page=\"660189f45f9033dd87f82a27\"
      data-wf-site=\"65b8cd72835ceeacd4449a53\" lang=\"en\"><head><meta charset=\"utf-8\"/><title>LangChain</title><meta
      content=\"LangChain\u2019s suite of products supports developers along each
      step of their development journey.\" name=\"description\"/><meta content=\"LangChain\"
      property=\"og:title\"/><meta content=\"LangChain\u2019s suite of products supports
      developers along each step of their development journey.\" property=\"og:description\"/><meta
      content=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65c69af1f3035ff97de2cc38_HomePage-metaImage.jpg\"
      property=\"og:image\"/><meta content=\"LangChain\" property=\"twitter:title\"/><meta
      content=\"LangChain\u2019s suite of products supports developers along each
      step of their development journey.\" property=\"twitter:description\"/><meta
      content=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65c69af1f3035ff97de2cc38_HomePage-metaImage.jpg\"
      property=\"twitter:image\"/><meta property=\"og:type\" content=\"website\"/><meta
      content=\"summary_large_image\" name=\"twitter:card\"/><meta content=\"width=device-width,
      initial-scale=1\" name=\"viewport\"/><link href=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/css/langchain-tonik.1c6f06df2.min.css\"
      rel=\"stylesheet\" type=\"text/css\"/><style>@media (min-width:992px) {html.w-mod-js:not(.w-mod-ix)
      [data-w-id=\"8202c0ba-560c-1974-f95f-1e4380661694\"] {opacity:0;}html.w-mod-js:not(.w-mod-ix)
      [data-w-id=\"4f634ffc-aabe-5b88-a321-3a55f4cd08d8\"] {opacity:0;}html.w-mod-js:not(.w-mod-ix)
      [data-w-id=\"00671cb6-faa2-aff9-f3ca-4d15160aa934\"] {opacity:0;}html.w-mod-js:not(.w-mod-ix)
      [data-w-id=\"43123052-e7a7-8a89-24f2-e32f8cae37e1\"] {opacity:0;}html.w-mod-js:not(.w-mod-ix)
      [data-w-id=\"f49f52e7-0f65-8bf9-79c6-b74cb4e015a5\"] {opacity:0;}}@media (max-width:991px)
      and (min-width:768px) {html.w-mod-js:not(.w-mod-ix) [data-w-id=\"4f634ffc-aabe-5b88-a321-3a55f4cd08d8\"]
      {opacity:0;}html.w-mod-js:not(.w-mod-ix) [data-w-id=\"00671cb6-faa2-aff9-f3ca-4d15160aa934\"]
      {opacity:0;}html.w-mod-js:not(.w-mod-ix) [data-w-id=\"43123052-e7a7-8a89-24f2-e32f8cae37e1\"]
      {opacity:0;}html.w-mod-js:not(.w-mod-ix) [data-w-id=\"f49f52e7-0f65-8bf9-79c6-b74cb4e015a5\"]
      {opacity:0;}}</style><script type=\"text/javascript\">!function(o,c){var n=c.documentElement,t=\"
      w-mod-\";n.className+=t+\"js\",(\"ontouchstart\"in o||o.DocumentTouch&&c instanceof
      DocumentTouch)&&(n.className+=t+\"touch\")}(window,document);</script><link
      href=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65c50ca4352352dd6a747e69_favicon.png\"
      rel=\"shortcut icon\" type=\"image/x-icon\"/><link href=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65c50ca94c9fe4ac7c52b415_webClip.png\"
      rel=\"apple-touch-icon\"/><link href=\"https://www.langchain.com/\" rel=\"canonical\"/><!--
      Start cookieyes banner --> \n<script id=\"cookieyes\" type=\"text/javascript\"
      src=\"https://cdn-cookieyes.com/client_data/d2e859934c4309be824ea8bb/script.js\">\n</script>
      \n<!-- End cookieyes banner -->\n<!-- Google tag (gtag.js) -->\n<script async
      src=\"https://www.googletagmanager.com/gtag/js?id=G-V1YFVJYTLP\"></script>\n<script>\n  window.dataLayer
      = window.dataLayer || [];\n  function gtag(){dataLayer.push(arguments);}\n  gtag(''js'',
      new Date());\n\n  gtag(''config'', ''G-V1YFVJYTLP'');\n</script> \n\n\n<script
      defer data-domain=\"langchain.com\" src=\"https://plausible.io/js/script.js\"></script>\n\n\n<!--
      Keep this css code to improve the font quality-->\n<style>\n  * {\n  -webkit-font-smoothing:
      antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  -o-font-smoothing: antialiased;\n}\n</style>\n\n<!--
      [Attributes by Finsweet] Disable scrolling -->\n<script defer src=\"https://cdn.jsdelivr.net/npm/@finsweet/attributes-scrolldisable@1/scrolldisable.js\"></script>\n</head><body><div
      class=\"page-wrapper\"><div class=\"global-styles w-embed\"><style>\n\n* {\n  -webkit-font-smoothing:
      antialiased;\n}\n\n.page-wrapper {\noverflow: clip;\n  }\n\n\n\n/* Set fluid
      size change for smaller breakpoints */\n  html { font-size: 1rem; }\n  @media
      screen and (max-width:1920px) and (min-width:1281px) { html { font-size: calc(0.2499999999999999rem
      + 0.6250000000000001vw); } }\n  @media screen and (max-width:1280px) and (min-width:992px)
      { html { font-size: calc(0.41223612197028925rem + 0.4222048475371384vw); } }\n/*
      video sizing */\n\nvideo {\n    object-fit: fill;\n\t\twidth: 100%;\n}\n\n\n\n#retrieval-video
      {\n    object-fit: cover;\n    width: 100%;\n}\n\n\n\n/* Set color style to
      inherit */\n.inherit-color * {\n    color: inherit;\n}\n\n/* Focus state style
      for keyboard navigation for the focusable elements */\n*[tabindex]:focus-visible,\n  input[type=\"file\"]:focus-visible
      {\n   outline: 0.125rem solid #4d65ff;\n   outline-offset: 0.125rem;\n}\n\n/*
      Get rid of top margin on first element in any rich text element */\n.w-richtext
      > :not(div):first-child, .w-richtext > div:first-child > :first-child {\n  margin-top:
      0 !important;\n}\n\n/* Get rid of bottom margin on last element in any rich
      text element */\n.w-richtext>:last-child, .w-richtext ol li:last-child, .w-richtext
      ul li:last-child {\n\tmargin-bottom: 0 !important;\n}\n\n/* Prevent all click
      and hover interaction with an element */\n.pointer-events-off {\n\tpointer-events:
      none;\n}\n\n/* Enables all click and hover interaction with an element */\n.pointer-events-on
      {\n  pointer-events: auto;\n}\n\n/* Create a class of .div-square which maintains
      a 1:1 dimension of a div */\n.div-square::after {\n\tcontent: \"\";\n\tdisplay:
      block;\n\tpadding-bottom: 100%;\n}\n\n/* Make sure containers never lose their
      center alignment */\n.container-medium,.container-small, .container-large {\n\tmargin-right:
      auto !important;\n  margin-left: auto !important;\n}\n\n/* \nMake the following
      elements inherit typography styles from the parent and not have hardcoded values.
      \nImportant: You will not be able to style for example \"All Links\" in Designer
      with this CSS applied.\nUncomment this CSS to use it in the project. Leave this
      message for future hand-off.\n*/\n/*\na,\n.w-input,\n.w-select,\n.w-tab-link,\n.w-nav-link,\n.w-dropdown-btn,\n.w-dropdown-toggle,\n.w-dropdown-link
      {\n  color: inherit;\n  text-decoration: inherit;\n  font-size: inherit;\n}\n*/\n\n/*
      Apply \"...\" after 3 lines of text */\n.text-style-3lines {\n\tdisplay: -webkit-box;\n\toverflow:
      hidden;\n\t-webkit-line-clamp: 3;\n\t-webkit-box-orient: vertical;\n}\n\n/*
      Apply \"...\" after 2 lines of text */\n.text-style-2lines {\n\tdisplay: -webkit-box;\n\toverflow:
      hidden;\n\t-webkit-line-clamp: 2;\n\t-webkit-box-orient: vertical;\n}\n\n/*
      Adds inline flex display */\n.display-inlineflex {\n  display: inline-flex;\n}\n\n/*
      These classes are never overwritten */\n.hide {\n  display: none !important;\n}\n\n@media
      screen and (max-width: 991px) {\n    .hide, .hide-tablet {\n        display:
      none !important;\n    }\n}\n  @media screen and (max-width: 767px) {\n    .hide-mobile-landscape{\n      display:
      none !important;\n    }\n}\n  @media screen and (max-width: 479px) {\n    .hide-mobile{\n      display:
      none !important;\n    }\n}\n \n.margin-0 {\n  margin: 0rem !important;\n}\n  \n.padding-0
      {\n  padding: 0rem !important;\n}\n\n.spacing-clean {\npadding: 0rem !important;\nmargin:
      0rem !important;\n}\n\n.margin-top {\n  margin-right: 0rem !important;\n  margin-bottom:
      0rem !important;\n  margin-left: 0rem !important;\n}\n\n.padding-top {\n  padding-right:
      0rem !important;\n  padding-bottom: 0rem !important;\n  padding-left: 0rem !important;\n}\n  \n.margin-right
      {\n  margin-top: 0rem !important;\n  margin-bottom: 0rem !important;\n  margin-left:
      0rem !important;\n}\n\n.padding-right {\n  padding-top: 0rem !important;\n  padding-bottom:
      0rem !important;\n  padding-left: 0rem !important;\n}\n\n.margin-bottom {\n  margin-top:
      0rem !important;\n  margin-right: 0rem !important;\n  margin-left: 0rem !important;\n}\n\n.padding-bottom
      {\n  padding-top: 0rem !important;\n  padding-right: 0rem !important;\n  padding-left:
      0rem !important;\n}\n\n.margin-left {\n  margin-top: 0rem !important;\n  margin-right:
      0rem !important;\n  margin-bottom: 0rem !important;\n}\n  \n.padding-left {\n  padding-top:
      0rem !important;\n  padding-right: 0rem !important;\n  padding-bottom: 0rem
      !important;\n}\n  \n.margin-horizontal {\n  margin-top: 0rem !important;\n  margin-bottom:
      0rem !important;\n}\n\n.padding-horizontal {\n  padding-top: 0rem !important;\n  padding-bottom:
      0rem !important;\n}\n\n.margin-vertical {\n  margin-right: 0rem !important;\n  margin-left:
      0rem !important;\n}\n  \n.padding-vertical {\n  padding-right: 0rem !important;\n  padding-left:
      0rem !important;\n}\n\n/* Apply \"...\" at 100% width */\n.truncate-width {
      \n\t\twidth: 100%; \n    white-space: nowrap; \n    overflow: hidden; \n    text-overflow:
      ellipsis; \n}\n/* Removes native scrollbar */\n.no-scrollbar {\n    -ms-overflow-style:
      none;\n    overflow: -moz-scrollbars-none; \n}\n\n.no-scrollbar::-webkit-scrollbar
      {\n    display: none;\n}\n\ninput:checked + span {\ncolor: white    /* styles
      for the div immediately following the checked input */\n}\n\n/* styles for word-wrapping\nh1,
      h2, h3 {\nword-wrap: break-word;\nhyphens: auto;\n}*/\n\n[nav-theme=\"light\"]
      .navbar_logo-svg {\n\t--nav--logo: var(--light--logo);\n}\n\n[nav-theme=\"light\"]
      .button.is-nav {\n\t--nav--button-bg: var(--light--button-bg);\n\t--nav--button-text:
      var(--light--button-text);\n}\n\n[nav-theme=\"light\"] .button.is-nav:hover
      {\n\t--nav--button-bg: var(--dark--button-bg);\n\t--nav--button-text:var(--dark--button-text);\n}\n\n[nav-theme=\"dark\"]
      .navbar_logo-svg {\n\t--nav--logo: var(--dark--logo);\n}\n\n[nav-theme=\"dark\"]
      .button.is-nav {\n\t--nav--button-bg: var(--dark--button-bg);\n\t--nav--button-text:
      var(--dark--button-text);\n}\n\n[nav-theme=\"dark\"] .button.is-nav:hover {\n\t--nav--button-bg:
      var(--light--button-bg);\n\t--nav--button-text: var(--light--button-text);\n}\n\n[nav-theme=\"red\"]
      .navbar_logo-svg {\n\t--nav--logo: var(--red--logo);\n}\n\n\n[nav-theme=\"red\"]
      .button.is-nav {\n\t--nav--button-bg: var(--red--button-bg);\n\t--nav--button-text:
      var(--red--button-text);\n}\n\n.navbar_logo-svg.is-light, .navbar_logo-svg.is-red.is-light{\ncolor:
      #F8F7FF!important;\n}\n\n.news_button[disabled] {\nbackground: none;\n}\n\n.product_bg-video
      video {\nobject-fit: fill;\n}\n\n</style></div><div data-animation=\"default\"
      class=\"navbar_component w-nav\" data-easing2=\"ease\" fs-scrolldisable-element=\"smart-nav\"
      data-easing=\"ease\" data-collapse=\"medium\" data-w-id=\"78839fc1-6b85-b108-b164-82fcae730868\"
      role=\"banner\" data-duration=\"400\"><div class=\"navbar_container\"><a href=\"/\"
      aria-current=\"page\" class=\"navbar_logo-link w-nav-brand w--current\"><div
      class=\"navbar_logo-svg w-embed\"><svg width=\"100%\" height=\"100%\" viewBox=\"0
      0 240 41\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path d=\"M61.5139
      11.1569C60.4527 11.1569 59.4549 11.568 58.708 12.3148L55.6899 15.3248C54.8757
      16.1368 54.4574 17.2643 54.5431 18.4202C54.5492 18.4833 54.5553 18.5464 54.5615
      18.6115C54.6696 19.4988 55.0594 20.2986 55.6899 20.9254C56.1246 21.3589 56.6041
      21.6337 57.1857 21.825C57.2163 22 57.2326 22.177 57.2326 22.3541C57.2326 23.1519
      56.9225 23.9008 56.3592 24.4625L56.1735 24.6477C55.1655 24.3037 54.3247 23.8011
      53.5656 23.044C52.5576 22.0386 51.8903 20.7687 51.6393 19.3747L51.6046 19.1813L51.4515
      19.3055C51.3475 19.3889 51.2495 19.4785 51.1577 19.57L48.1396 22.58C46.5928
      24.1226 46.5928 26.636 48.1396 28.1786C48.913 28.9499 49.9292 29.3366 50.9475
      29.3366C51.9658 29.3366 52.98 28.9499 53.7534 28.1786L56.7715 25.1687C58.3183
      23.626 58.3183 21.1147 56.7715 19.57C56.3592 19.159 55.8675 18.8496 55.3104
      18.6502C55.2798 18.469 55.2634 18.2879 55.2634 18.1109C55.2634 17.2439 55.6063
      16.4217 56.2348 15.7949C57.2449 16.1388 58.1407 16.6965 58.8978 17.4515C59.9038
      18.4548 60.5691 19.7227 60.8241 21.1208L60.8588 21.3141L61.0119 21.19C61.116
      21.1066 61.2139 21.017 61.3078 20.9234L64.3259 17.9135C65.8727 16.3708 65.8747
      13.8575 64.3259 12.3148C63.577 11.568 62.5811 11.1569 61.518 11.1569H61.5139Z\"
      fill=\"CurrentColor\"/>\n<path d=\"M59.8966 0.148865H20.4063C9.15426 0.148865
      0 9.27841 0 20.5001C0 31.7217 9.15426 40.8513 20.4063 40.8513H59.8966C71.1486
      40.8513 80.3029 31.7217 80.3029 20.5001C80.3029 9.27841 71.1486 0.148865 59.8966
      0.148865ZM40.4188 32.0555C39.7678 32.1898 39.0352 32.2142 38.5373 31.6953C38.3536
      32.1165 37.9251 31.8947 37.5945 31.8398C37.5639 31.9252 37.5374 32.0005 37.5088
      32.086C36.4089 32.1593 35.5845 31.04 35.0601 30.1954C34.0193 29.6337 32.8378
      29.2918 31.7746 28.7036C31.7134 29.6724 31.9257 30.8731 31.0012 31.4979C30.9543
      33.36 33.8255 31.7177 34.0887 33.1056C33.8847 33.128 33.6582 33.073 33.4949
      33.2297C32.746 33.9563 31.8869 32.6803 31.0237 33.2074C29.8646 33.7894 29.7483
      34.2656 28.3137 34.3857C28.2342 34.2656 28.2668 34.1862 28.3341 34.113C28.7382
      33.6449 28.7668 33.0934 29.4565 32.8939C28.7464 32.782 28.1525 33.1728 27.5546
      33.4821C26.7771 33.7996 26.7833 32.7657 25.5875 33.537C25.4548 33.4292 25.5181
      33.3315 25.5936 33.2481C25.8976 32.8777 26.2976 32.8227 26.7486 32.8431C24.5304
      31.6098 23.4856 34.3511 22.4612 32.9876C22.1531 33.069 22.0368 33.3457 21.8429
      33.5411C21.6756 33.358 21.8021 33.1361 21.8103 32.9204C21.6103 32.8268 21.3572
      32.782 21.4164 32.4625C21.0246 32.3302 20.7512 32.5622 20.4594 32.782C20.1961
      32.5785 20.6369 32.2814 20.7185 32.0697C20.9532 31.6627 21.4878 31.9863 21.7592
      31.6932C22.5306 31.2557 23.606 31.9659 24.4876 31.8459C25.1671 31.9313 26.0078
      31.2353 25.667 30.5413C24.9406 29.6154 25.0691 28.4045 25.0528 27.2974C24.963
      26.6522 23.4101 25.83 22.9612 25.134C22.4061 24.5072 21.9735 23.7807 21.5409
      23.0664C19.9798 20.0523 20.4716 16.1795 18.5044 13.3812C17.6147 13.8717 16.4556
      13.6397 15.6884 12.9823C15.2741 13.3588 15.2557 13.8513 15.2231 14.3744C14.2293
      13.3833 14.3538 11.5109 15.1476 10.4079C15.4721 9.97239 15.8598 9.61421 16.2924
      9.29876C16.3903 9.22754 16.423 9.15834 16.4209 9.04844C17.2066 5.52362 22.5653
      6.20335 24.259 8.70044C25.4875 10.237 25.8589 12.27 27.2526 13.6967C29.1279
      15.744 31.2645 17.5471 32.9949 19.7267C34.6315 21.7191 35.8008 24.0554 36.8211
      26.4101C37.2374 27.1915 37.2415 28.1501 37.8578 28.8176C38.1618 29.2206 39.6474
      30.3175 39.325 30.7062C39.5107 31.1091 40.8983 31.6647 40.4167 32.0555H40.4188ZM66.4449
      20.032L63.4269 23.0419C62.6228 23.8438 61.6291 24.4421 60.5516 24.7697L60.4965
      24.786L60.4761 24.8389C60.1251 25.7629 59.5925 26.5871 58.8905 27.2852L55.8724
      30.2951C54.5562 31.6078 52.8054 32.3302 50.9402 32.3302C49.0751 32.3302 47.3242
      31.6078 46.008 30.2951C43.2879 27.5823 43.2879 23.1701 46.008 20.4573L49.0261
      17.4474C49.8342 16.6415 50.8015 16.0615 51.8973 15.7257L51.9524 15.7094L51.9728
      15.6565C52.3238 14.7325 52.8584 13.9063 53.5625 13.2021L56.5805 10.1922C57.8967
      8.87953 59.6476 8.15706 61.5127 8.15706C63.3779 8.15706 65.1287 8.87953 66.4449
      10.1922C67.7612 11.5048 68.4856 13.251 68.4856 15.1111C68.4856 16.9712 67.7612
      18.7193 66.4449 20.03V20.032Z\" fill=\"CurrentColor\"/>\n<path d=\"M28.1422
      28.4126C27.8769 29.4424 27.7912 31.1946 26.4485 31.2455C26.3383 31.8398 26.8607
      32.0636 27.3382 31.8723C27.8096 31.6566 28.034 32.0433 28.1932 32.4279C28.9217
      32.5337 29.9992 32.1857 30.04 31.3249C28.9523 30.7001 28.6156 29.5116 28.1442
      28.4106L28.1422 28.4126Z\" fill=\"CurrentColor\"/>\n<path d=\"M99.2089 10.8162H95.624V32.5623H111V29.0983H99.2089V10.8162Z\"
      fill=\"CurrentColor\"/>\n<path d=\"M129.021 32.5623H132.606V32.5236H132.653L132.66
      32.364C132.661 32.3173 132.674 31.8821 132.608 31.2115V23.1182C132.608 20.0733
      134.828 18.6871 136.891 18.6871C139.11 18.6871 140.19 19.8831 140.19 22.3445V32.5623H143.775V21.8674C143.775
      17.8054 141.194 15.2812 137.041 15.2812C135.276 15.2812 133.701 15.7825 132.464
      16.7351L132.431 15.5794H129.026V32.5623H129.021Z\" fill=\"CurrentColor\"/>\n<path
      d=\"M158.365 16.827C157.119 15.8147 155.517 15.2812 153.71 15.2812C148.876 15.2812
      145.873 18.6146 145.873 23.9806C145.873 29.3466 148.876 32.7106 153.71 32.7106C155.415
      32.7106 156.937 32.2464 158.136 31.363C158.033 33.9953 156.384 35.5588 153.681
      35.5588C151.408 35.5588 150.122 34.8351 149.858 33.4085L149.825 33.2312L146.342
      34.2935L146.373 34.4369C146.961 37.2722 149.614 38.9647 153.473 38.9647C156.091
      38.9647 158.144 38.2523 159.577 36.8451C161.023 35.4266 161.755 33.3827 161.755
      30.7715V15.5794H158.439L158.367 16.827H158.365ZM158.141 24.1305C158.141 27.3704
      156.563 29.3063 153.918 29.3063C151.083 29.3063 149.458 27.3655 149.458 23.9822C149.458
      20.5988 151.084 18.6871 153.918 18.6871C156.499 18.6871 158.115 20.6133 158.141
      23.713V24.1305Z\" fill=\"CurrentColor\"/>\n<path d=\"M179.418 25.6665C178.675
      28.0698 176.767 29.3948 174.047 29.3948C170.158 29.3948 167.74 26.4418 167.74
      21.6884C167.74 16.9349 170.179 13.9819 174.107 13.9819C176.825 13.9819 178.391
      15.0474 179.188 17.4346L179.56 18.5516L182.945 16.9623L182.627 16.0677C181.338
      12.4361 178.361 10.5179 174.018 10.5179C171.067 10.5179 168.562 11.5834 166.773
      13.5983C165.003 15.5938 164.066 18.3904 164.066 21.69C164.066 28.476 167.983
      32.862 174.047 32.862C178.32 32.862 181.63 30.6263 182.9 26.8803L183.224 25.9228L179.747
      24.601L179.416 25.6681L179.418 25.6665Z\" fill=\"CurrentColor\"/>\n<path d=\"M192.806
      15.2812C191.094 15.2812 189.571 15.7503 188.375 16.6417V8.73212H184.79V32.5639H188.375V23.1199C188.375
      20.0557 190.594 18.6598 192.658 18.6598C194.877 18.6598 195.957 19.8558 195.957
      22.3171V32.5656H199.542V21.84C199.542 17.857 196.899 15.2845 192.808 15.2845L192.806
      15.2812Z\" fill=\"CurrentColor\"/>\n<path d=\"M220.496 8.21954C219.164 8.21954
      218.197 9.18668 218.197 10.5181C218.197 11.8495 219.164 12.8167 220.496 12.8167C221.827
      12.8167 222.794 11.8495 222.794 10.5181C222.794 9.18668 221.827 8.21954 220.496
      8.21954Z\" fill=\"CurrentColor\"/>\n<path d=\"M233.262 15.2812C231.497 15.2812
      229.923 15.7825 228.686 16.7351L228.652 15.5794H225.248V32.5623H228.833V23.1182C228.833
      20.0733 231.052 18.6871 233.116 18.6871C235.335 18.6871 236.415 19.8831 236.415
      22.3445V32.5623H240V21.8674C240 17.8054 237.419 15.2812 233.266 15.2812H233.262Z\"
      fill=\"CurrentColor\"/>\n<path d=\"M222.237 15.5794H218.67V23.9984C217.68 23.1666
      216.514 22.538 215.198 22.127V21.271C215.198 17.5202 212.736 15.2812 208.611
      15.2812C205.26 15.2812 202.754 16.8545 201.733 19.5963L201.458 20.3361L204.332
      22.4542L204.825 21.1695C205.475 19.4754 206.679 18.6856 208.611 18.6856C210.544
      18.6856 211.613 19.6156 211.613 21.4483V21.5435C211.498 21.5386 211.382 21.5354
      211.266 21.5338C207.428 21.4725 204.628 22.3704 202.944 24.1982C201.221 26.068
      201.372 28.1683 201.395 28.4004L201.411 28.5616H201.427C201.695 31.1777 203.98
      32.8573 207.301 32.8573C209.13 32.8573 210.82 32.348 212.098 31.4179L212.112
      32.5607H215.198V27.2479L215.131 27.1996C214.695 26.8804 213.934 26.4678 212.789
      26.3227C212.43 26.2776 212.087 26.255 211.771 26.2615H211.611V26.7499C211.611
      27.8734 210.32 29.4546 207.448 29.4546C205.328 29.4546 205.012 28.5632 205.012
      28.0313V27.9765C205.028 27.738 205.128 27.1544 205.655 26.6112C206.324 25.9197
      207.83 25.1106 211.207 25.1622C213.652 25.2008 215.528 25.8843 216.784 27.1947C218.325
      28.8034 218.615 31.0053 218.668 31.8676V32.5623H222.235V15.5794H222.237Z\" fill=\"CurrentColor\"/>\n<path
      d=\"M119.752 15.2071C116.401 15.2071 113.895 16.7803 112.874 19.5221L112.599
      20.262L115.473 22.38L115.966 21.0953C116.616 19.4012 117.82 18.6114 119.752
      18.6114C121.685 18.6114 122.754 19.5415 122.754 21.3742V21.8046L119.062 22.4558C114.734
      23.2214 112.539 25.0719 112.539 27.9539C112.539 30.836 114.855 32.7832 118.44
      32.7832C120.27 32.7832 121.959 32.2738 123.237 31.3437L123.252 32.4866H126.337V21.1969C126.337
      17.446 123.875 15.2071 119.751 15.2071H119.752ZM122.754 25.1638V26.6773C122.754
      27.8008 121.462 29.3821 118.59 29.3821C116.47 29.3821 116.155 28.4907 116.155
      27.9588C116.155 27.4865 116.155 26.3807 119.91 25.6844L122.754 25.1654V25.1638Z\"
      fill=\"CurrentColor\"/>\n</svg></div></a><nav role=\"navigation\" class=\"navbar_menu
      is-page-height-tablet w-nav-menu\"><div class=\"navbar_navlinks\"><div data-hover=\"true\"
      data-delay=\"800\" data-w-id=\"86f16cab-d26d-6ec0-1e8b-7f5d62764942\" class=\"navbar_menu-dropdown
      w-dropdown\"><div class=\"navbar_dropdown-toggle w-dropdown-toggle\"><div>Products</div><div
      class=\"dropdown-chevron w-embed\"><svg width=\"100%\" height=\"100%\" viewBox=\"0
      0 16 9\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path d=\"M1.5
      1L8 7.5L14.5 1\" stroke=\"CurrentColor\" stroke-width=\"2\"/>\n</svg></div></div><nav
      class=\"navbar_dropdown-list w-dropdown-list\"><div class=\"navbar_dropdown-link-list\"><div
      id=\"w-node-_86f16cab-d26d-6ec0-1e8b-7f5d62764949-62764940\" class=\"dropdown_links-wrap
      is-logos\"><a href=\"/langchain\" class=\"navbar_navlink\">LangChain</a><a href=\"/langsmith\"
      class=\"navbar_navlink\">LangSmith</a><a href=\"/langgraph\" class=\"navbar_navlink\">LangGraph</a></div></div></nav></div><div
      data-hover=\"true\" data-delay=\"200\" data-w-id=\"86f16cab-d26d-6ec0-1e8b-7f5d62764950\"
      class=\"navbar_menu-dropdown w-dropdown\"><div class=\"navbar_dropdown-toggle
      w-dropdown-toggle\"><div>Methods</div><div class=\"dropdown-chevron w-embed\"><svg
      width=\"100%\" height=\"100%\" viewBox=\"0 0 16 9\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path
      d=\"M1.5 1L8 7.5L14.5 1\" stroke=\"CurrentColor\" stroke-width=\"2\"/>\n</svg></div></div><nav
      class=\"navbar_dropdown-list w-dropdown-list\"><div class=\"navbar_dropdown-link-list\"><div
      id=\"w-node-_86f16cab-d26d-6ec0-1e8b-7f5d62764957-62764940\" class=\"dropdown_links-wrap\"><a
      id=\"w-node-_86f16cab-d26d-6ec0-1e8b-7f5d62764958-62764940\" href=\"/retrieval\"
      class=\"navbar_navlink\">Retrieval</a><a id=\"w-node-_86f16cab-d26d-6ec0-1e8b-7f5d6276495a-62764940\"
      href=\"/agents\" class=\"navbar_navlink\">Agents</a><a id=\"w-node-_86f16cab-d26d-6ec0-1e8b-7f5d6276495c-62764940\"
      href=\"/evaluation\" class=\"navbar_navlink\">Evaluation</a></div></div></nav></div><div
      data-hover=\"true\" data-delay=\"200\" data-w-id=\"86f16cab-d26d-6ec0-1e8b-7f5d6276495e\"
      class=\"navbar_menu-dropdown w-dropdown\"><div class=\"navbar_dropdown-toggle
      w-dropdown-toggle\"><div>Resources</div><div class=\"dropdown-chevron w-embed\"><svg
      width=\"100%\" height=\"100%\" viewBox=\"0 0 16 9\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path
      d=\"M1.5 1L8 7.5L14.5 1\" stroke=\"CurrentColor\" stroke-width=\"2\"/>\n</svg></div></div><nav
      class=\"navbar_dropdown-list w-dropdown-list\"><div class=\"navbar_dropdown-link-list\"><div
      id=\"w-node-_51e31229-b145-848e-f1dc-e92c526909bc-62764940\" class=\"dropdown_links-wrap\"><a
      id=\"w-node-_51e31229-b145-848e-f1dc-e92c526909bd-62764940\" href=\"https://blog.langchain.dev/\"
      target=\"_blank\" class=\"navbar_navlink\">Blog</a><a id=\"w-node-_51e31229-b145-848e-f1dc-e92c526909bf-62764940\"
      href=\"/case-studies\" class=\"navbar_navlink\">Case Studies</a><a id=\"w-node-_51e31229-b145-848e-f1dc-e92c526909c1-62764940\"
      href=\"https://academy.langchain.com/\" class=\"navbar_navlink\">LangChain Academy</a><a
      id=\"w-node-_504adce7-2ba3-85f7-90da-f3a703f2243a-62764940\" href=\"https://lu.ma/langchain\"
      target=\"_blank\" class=\"navbar_navlink\">Events</a><a id=\"w-node-_51e31229-b145-848e-f1dc-e92c526909c3-62764940\"
      href=\"/experts\" class=\"navbar_navlink\">Experts</a><a id=\"w-node-_51e31229-b145-848e-f1dc-e92c526909c5-62764940\"
      href=\"https://changelog.langchain.com/\" target=\"_blank\" class=\"navbar_navlink\">Changelog</a></div></div></nav></div><div
      data-hover=\"true\" data-delay=\"200\" data-w-id=\"86f16cab-d26d-6ec0-1e8b-7f5d6276497c\"
      class=\"navbar_menu-dropdown w-dropdown\"><div class=\"navbar_dropdown-toggle
      w-dropdown-toggle\"><div>Docs</div><div class=\"dropdown-chevron w-embed\"><svg
      width=\"100%\" height=\"100%\" viewBox=\"0 0 16 9\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path
      d=\"M1.5 1L8 7.5L14.5 1\" stroke=\"CurrentColor\" stroke-width=\"2\"/>\n</svg></div></div><nav
      class=\"navbar_dropdown-list w-dropdown-list\"><div class=\"navbar_dropdown-link-list
      top\"><div id=\"w-node-e5c2bc0a-1422-8e6e-28af-2dbdd9cff1f5-62764940\" class=\"text-size-small
      text-weight-semibold\">Python</div><div id=\"w-node-e5c2bc0a-1422-8e6e-28af-2dbdd9cff1f7-62764940\"
      class=\"dropdown_links-wrap\"><a id=\"w-node-_1ff6dbe6-7771-84f4-414b-58f61076d9ed-62764940\"
      href=\"https://python.langchain.com/v0.2/docs/introduction/\" target=\"_blank\"
      class=\"navbar_navlink\">LangChain</a><a id=\"w-node-_4df7c45a-9a40-888d-5455-7107ee4cd4a3-62764940\"
      href=\"https://docs.smith.langchain.com/\" target=\"_blank\" class=\"navbar_navlink\">LangSmith</a><a
      id=\"w-node-_8e20603b-52bd-d3aa-eb3f-c4cf86c29851-62764940\" href=\"https://langchain-ai.github.io/langgraph/tutorials/introduction/\"
      target=\"_blank\" class=\"navbar_navlink\">LangGraph</a></div></div><div class=\"navbar_dropdown-link-list
      bottom\"><div id=\"w-node-a3462865-b5b0-23ec-62f2-d982c6254db9-62764940\" class=\"text-size-small
      text-weight-semibold\">JavaScript</div><div id=\"w-node-a3462865-b5b0-23ec-62f2-d982c6254dbb-62764940\"
      class=\"dropdown_links-wrap\"><a id=\"w-node-_6f08623a-c042-fd92-80b0-6af2464dde5b-62764940\"
      href=\"https://js.langchain.com/v0.2/docs/tutorials/\" target=\"_blank\" class=\"navbar_navlink\">LangChain</a><a
      id=\"w-node-_35b67bfd-02a3-b2d3-0ef5-149e574bcd3a-62764940\" href=\"https://docs.smith.langchain.com/\"
      target=\"_blank\" class=\"navbar_navlink\">LangSmith</a><a id=\"w-node-_3e895595-200d-8649-01f6-24dfab935ded-62764940\"
      href=\"https://langchain-ai.github.io/langgraphjs/tutorials/quickstart/\" target=\"_blank\"
      class=\"navbar_navlink\">LangGraph</a></div></div></nav></div><div data-hover=\"true\"
      data-delay=\"200\" data-w-id=\"86f16cab-d26d-6ec0-1e8b-7f5d6276498a\" class=\"navbar_menu-dropdown
      w-dropdown\"><div class=\"navbar_dropdown-toggle w-dropdown-toggle\"><div>Company</div><div
      class=\"dropdown-chevron w-embed\"><svg width=\"100%\" height=\"100%\" viewBox=\"0
      0 16 9\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path d=\"M1.5
      1L8 7.5L14.5 1\" stroke=\"CurrentColor\" stroke-width=\"2\"/>\n</svg></div></div><nav
      class=\"navbar_dropdown-list w-dropdown-list\"><div class=\"navbar_dropdown-link-list\"><div
      id=\"w-node-_86f16cab-d26d-6ec0-1e8b-7f5d62764991-62764940\" class=\"dropdown_links-wrap\"><a
      id=\"w-node-_86f16cab-d26d-6ec0-1e8b-7f5d62764992-62764940\" href=\"/about\"
      class=\"navbar_navlink\">About</a><a id=\"w-node-_86f16cab-d26d-6ec0-1e8b-7f5d62764994-62764940\"
      href=\"/careers\" class=\"navbar_navlink\">Careers</a><link rel=\"prefetch\"
      href=\"/careers\"/></div></div></nav></div><a href=\"/pricing\" class=\"navbar_link
      is-pricing menu\">Pricing</a></div><div class=\"navbar_menu-buttons\"><a href=\"/contact-sales\"
      class=\"button is-nav-copy w-button\">Get a demo</a></div><div class=\"navbar_menu-buttons\"><a
      href=\"https://smith.langchain.com/\" target=\"_blank\" class=\"button is-nav
      w-button\">Sign up</a></div></nav><div id=\"menu-toggle\" class=\"navbar_menu-button
      w-nav-button\"><div class=\"menu-icon\"><div class=\"menu-icon_line-top\"></div><div
      class=\"menu-icon_line-middle\"><div class=\"menu-icon_line-middle-inner\"></div></div><div
      class=\"menu-icon_line-bottom\"></div></div></div></div><div class=\"navbar_menu-bg\"></div><div
      class=\"navbar_scroll-bg\"></div></div><main class=\"main-wrapper\"><header
      class=\"section_header\"><div class=\"padding-global\"><div class=\"container-large\"><div
      class=\"header_content\"><div class=\"header_title-wrap is-home\"><div class=\"maxwidth-header-home\"><p
      class=\"text-size-large hide\">LangChain\u2019s suite of products supports developers
      along each step of the LLM application lifecycle.</p></div><h1 data-w-id=\"8202c0ba-560c-1974-f95f-1e4380661694\">Applications
      that can reason. Powered by LangChain.</h1></div><div class=\"join-us_buttons-wrapper\"><a
      href=\"/contact-sales\" class=\"button w-button\">Get a demo</a><a href=\"https://smith.langchain.com/\"
      target=\"_blank\" class=\"button is-alternate w-button\">Sign up for free</a></div></div></div></div><div
      class=\"header_background-video-wrapper\"><div class=\"video-wrap w-embed\"><video
      autoplay muted loop playsinline id=\"myVideo\" poster=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65c8ed3e7d491e37259a30c5_Langchain-hero-1_1706794335%201-placeholder.jpg\">\n  <source
      data-src=\"https://customer-xp1a3vy0ydc4ega7.cloudflarestream.com/bb6cf069546e3d829aa5808ac8b07748/downloads/default.mp4\"
      type=\"video/mp4\">\n</video></div></div></header><section class=\"section_home-logos\"><div
      class=\"padding-global\"><div class=\"container-large\"><div class=\"home-centered-logos\"><h2
      data-w-id=\"4f634ffc-aabe-5b88-a321-3a55f4cd08d8\">From startups to global enterprises,
      <br/>ambitious builders choose <br/>LangChain products.</h2></div></div></div><div
      data-w-id=\"e0bd309c-bace-a92d-af76-e546c1bb3f8c\" style=\"opacity:0\" class=\"logo_component
      is-home\"><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ca3b7c22746faa78338532_logo_Ally.svg\"
      loading=\"lazy\" width=\"35.5\" alt=\"\" class=\"logo_logo ally\"/><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ca3b7c08e67bb7eefba4c2_logo_Rakuten.svg\"
      loading=\"lazy\" alt=\"\" class=\"logo_logo\"/><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ca3b7c576fdde32d03c1a0_logo_Elastic.svg\"
      loading=\"lazy\" alt=\"\" class=\"logo_logo\"/><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ca3b7c6d5592036dae24e5_logo_BCG.svg\"
      loading=\"lazy\" alt=\"\" class=\"logo_logo\"/><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ca3b7cbcf6473519b06d84_logo_IDEO.svg\"
      loading=\"lazy\" alt=\"\" class=\"logo_logo\"/><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/6606183e52d49bc369acc76c_mdy_logo_rgb_moodysblue.png\"
      loading=\"lazy\" width=\"66\" sizes=\"(max-width: 991px) 95.953125px, (max-width:
      1279px) 9vw, (max-width: 1439px) 7vw, 95.953125px\" alt=\"\" srcset=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/6606183e52d49bc369acc76c_mdy_logo_rgb_moodysblue-p-500.png
      500w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/6606183e52d49bc369acc76c_mdy_logo_rgb_moodysblue-p-800.png
      800w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/6606183e52d49bc369acc76c_mdy_logo_rgb_moodysblue-p-1080.png
      1080w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/6606183e52d49bc369acc76c_mdy_logo_rgb_moodysblue-p-1600.png
      1600w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/6606183e52d49bc369acc76c_mdy_logo_rgb_moodysblue-p-2000.png
      2000w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/6606183e52d49bc369acc76c_mdy_logo_rgb_moodysblue-p-2600.png
      2600w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/6606183e52d49bc369acc76c_mdy_logo_rgb_moodysblue-p-3200.png
      3200w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/6606183e52d49bc369acc76c_mdy_logo_rgb_moodysblue.png
      4086w\" class=\"logo_logo\"/><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ca3b7c8ad7db6ed6ec611e_logo_Adyen.svg\"
      loading=\"lazy\" width=\"72.5\" alt=\"\" class=\"logo_logo\"/><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ca3b7c737d50036a62768b_logo_Infor.svg\"
      loading=\"lazy\" width=\"38\" alt=\"\" class=\"logo_logo infor\"/><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/66d25beedcf257853b52eb88_eventbrite.png\"
      loading=\"lazy\" width=\"38\" sizes=\"(max-width: 991px) 95.953125px, (max-width:
      1279px) 7vw, (max-width: 1439px) 6vw, (max-width: 1919px) 5vw, 95.953125px\"
      alt=\"\" srcset=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/66d25beedcf257853b52eb88_eventbrite-p-500.png
      500w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/66d25beedcf257853b52eb88_eventbrite.png
      800w\" class=\"logo_logo infor\"/><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667f59d98444a5f98aabe21c_acxiom-vector-logo-2022%201.png\"
      loading=\"lazy\" width=\"38\" sizes=\"(max-width: 991px) 95.953125px, (max-width:
      1279px) 9vw, (max-width: 1439px) 7vw, 95.953125px\" alt=\"\" srcset=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667f59d98444a5f98aabe21c_acxiom-vector-logo-2022%201-p-500.png
      500w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667f59d98444a5f98aabe21c_acxiom-vector-logo-2022%201.png
      652w\" class=\"logo_logo\"/><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ca3b7c09a158ffeaab0bd2_logo_Replit.svg\"
      loading=\"lazy\" width=\"86\" alt=\"\" class=\"logo_logo\"/><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ca3b7c9d2b23d292a0cab0_logo_Retool.svg\"
      loading=\"lazy\" width=\"76.5\" alt=\"\" class=\"logo_logo\"/><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ca3b7c44e67a3d0a996bf3_logo_Databricks.svg\"
      loading=\"lazy\" width=\"98\" alt=\"\" class=\"logo_logo\"/><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667f5a1299d6ba453c78a849_image%20(19).png\"
      loading=\"lazy\" width=\"84\" sizes=\"(max-width: 991px) 95.953125px, (max-width:
      1279px) 9vw, (max-width: 1439px) 7vw, 95.953125px\" alt=\"\" srcset=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667f5a1299d6ba453c78a849_image%20(19)-p-500.png
      500w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667f5a1299d6ba453c78a849_image%20(19).png
      600w\" class=\"logo_logo\"/><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ca3b7c63af578816bafcc3_logo_Instacart.svg\"
      loading=\"lazy\" width=\"72.5\" alt=\"\" class=\"logo_logo\"/><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/665dc1dabc940168384d9596_podium%20logo.svg\"
      loading=\"lazy\" width=\"72.5\" height=\"Auto\" alt=\"\" class=\"logo_logo\"/><img
      src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667f19528c3557c2c19c3086_the-home-depot-2%201.png\"
      loading=\"lazy\" sizes=\"(max-width: 991px) 95.953125px, (max-width: 1279px)
      7vw, (max-width: 1439px) 6vw, (max-width: 1919px) 5vw, 95.953125px\" alt=\"\"
      width=\"Auto\" srcset=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667f19528c3557c2c19c3086_the-home-depot-2%201-p-500.png
      500w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667f19528c3557c2c19c3086_the-home-depot-2%201-p-800.png
      800w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667f19528c3557c2c19c3086_the-home-depot-2%201-p-1080.png
      1080w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667f19528c3557c2c19c3086_the-home-depot-2%201-p-1600.png
      1600w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667f19528c3557c2c19c3086_the-home-depot-2%201-p-2000.png
      2000w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667f19528c3557c2c19c3086_the-home-depot-2%201.png
      2500w\" class=\"logo_logo home\"/><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/66c4b0b2399590cbe63b0b3c_Vector%20(1).png\"
      loading=\"lazy\" alt=\"\" class=\"logo_logo bridge\"/></div></section><section
      class=\"section_build-observe-deploy\"><div class=\"padding-global\"><div class=\"container-large\"><div
      class=\"bod_wrapper\"><div class=\"bud_single\"><div class=\"bud_top-border\"></div><div
      class=\"bud_text\"><p class=\"heading-style-decorative-1 is-alternate\">Build</p><p>LangChain
      is a framework to build with LLMs by chaining interoperable components. LangGraph
      is the framework for building controllable agentic workflows.</p></div><div
      class=\"bud_bottom-border\"></div></div><div class=\"bud_single\"><div class=\"bud_top-border\"></div><div
      class=\"bud_text\"><p class=\"heading-style-decorative-1 is-alternate\">Run</p><p>Deploy
      your LLM applications at scale with LangGraph Cloud, our infrastructure purpose-built
      for agents.</p></div><div class=\"bud_bottom-border\"></div></div><div class=\"bud_single\"><div
      class=\"bud_top-border\"></div><div class=\"bud_text\"><p class=\"heading-style-decorative-1
      is-alternate\">Manage</p><p>Debug, collaborate, test, and monitor your LLM app
      in LangSmith - whether it&#x27;s built with a LangChain framework or not.\u00a0</p></div><div
      class=\"bud_bottom-border\"></div></div></div></div></div></section><section
      class=\"section_home-products\"><div class=\"padding-global\"><div class=\"container-xlarge\"><div
      class=\"home_products-wrapper\"><div class=\"w-layout-grid product_component
      is-inverted\"><div class=\"home_build-video-wrapper\"><div data-poster-url=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ce012c99a9683732d528cf_retrieval-video-poster-00001.jpg\"
      data-video-urls=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ce012c99a9683732d528cf_retrieval-video-transcode.mp4,https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ce012c99a9683732d528cf_retrieval-video-transcode.webm\"
      data-autoplay=\"true\" data-loop=\"true\" data-wf-ignore=\"true\" class=\"product_bg-video
      w-background-video w-background-video-atom\"><video id=\"d01e6884-4b74-66ba-e25f-e5027d12b585-video\"
      autoplay=\"\" loop=\"\" style=\"background-image:url(&quot;https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ce012c99a9683732d528cf_retrieval-video-poster-00001.jpg&quot;)\"
      muted=\"\" playsinline=\"\" data-wf-ignore=\"true\" data-object-fit=\"cover\"><source
      src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ce012c99a9683732d528cf_retrieval-video-transcode.mp4\"
      data-wf-ignore=\"true\"/><source src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ce012c99a9683732d528cf_retrieval-video-transcode.webm\"
      data-wf-ignore=\"true\"/></video></div></div><div id=\"w-node-_1ed64ebd-ae61-1759-b42b-07a447b5c121-87f82a27\"
      class=\"product_text-wrapper\"><div class=\"product_text-content\"><div class=\"icon-embed-custom
      w-embed\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"100%\" height=\"100%\"
      viewBox=\"0 0 77 39\" fill=\"none\" preserveAspectRatio=\"xMidYMid meet\" aria-hidden=\"true\"
      role=\"img\">\n<path d=\"M58.9837 10.5483C57.9651 10.5483 57.0112 10.9424 56.2919
      11.657L53.399 14.5423C52.6171 15.3215 52.2172 16.4012 52.2999 17.5077C52.3066
      17.5678 52.3111 17.6301 52.3178 17.6903C52.4228 18.5407 52.7958 19.3065 53.399
      19.9076C53.8167 20.3239 54.3305 20.6401 54.8867 20.8226C54.9158 20.9896 54.9314
      21.161 54.9314 21.3302C54.9314 22.0938 54.6343 22.8107 54.0937 23.3495L53.915
      23.5276C52.9477 23.1981 52.0899 22.6638 51.3594 21.938C50.3922 20.974 49.7533
      19.7585 49.512 18.4227L49.4785 18.2357L49.3311 18.3559C49.2328 18.4361 49.1367
      18.5229 49.0496 18.6097L46.1545 21.495C44.6712 22.9732 44.6712 25.3798 46.1545
      26.8603C46.8961 27.5994 47.8723 27.969 48.8463 27.969C49.8203 27.969 50.7965
      27.5994 51.5381 26.8603L54.4333 23.975C55.9166 22.4968 55.9166 20.0902 54.4333
      18.6119C54.0379 18.2179 53.5665 17.9218 53.0326 17.7303C53.0036 17.5567 52.9879
      17.383 52.9879 17.2138C52.9879 16.3834 53.3186 15.5953 53.9195 14.9942C54.889
      15.3237 55.7468 15.858 56.4728 16.5816C57.4379 17.5433 58.0767 18.7589 58.3202
      20.0969L58.3538 20.2839L58.5012 20.1637C58.5995 20.0835 58.6955 19.9967 58.7849
      19.9076L61.68 17.0246C63.1633 15.5463 63.1633 13.1397 61.68 11.6593C60.9629
      10.9446 60.0068 10.5506 58.9882 10.5506L58.9837 10.5483Z\" fill=\"currentColor\"/>\n<path
      d=\"M57.4333 0H19.5667C8.77696 0 0 8.74706 0 19.5C0 30.2529 8.77696 39 19.5667
      39H57.4333C68.223 39 77 30.2529 77 19.5C77 8.74706 68.223 0 57.4333 0ZM38.7558
      30.5735C38.1303 30.7027 37.4288 30.7271 36.953 30.2285C36.7766 30.6314 36.3677
      30.4199 36.0483 30.3665C36.0193 30.4489 35.9947 30.5201 35.9656 30.6025C34.9113
      30.6737 34.1205 29.6006 33.6178 28.7903C32.6193 28.2537 31.4867 27.9243 30.468
      27.361C30.4077 28.2894 30.6132 29.4381 29.7264 30.0392C29.6817 31.8225 32.4361
      30.2485 32.6863 31.5798C32.4897 31.5998 32.273 31.5486 32.1167 31.7C31.3996
      32.3969 30.573 31.1746 29.7465 31.6778C28.6363 32.2366 28.5223 32.6907 27.1485
      32.8065C27.0725 32.6929 27.1038 32.615 27.1686 32.546C27.555 32.0985 27.5819
      31.5687 28.2453 31.3772C27.564 31.2704 26.9943 31.6444 26.4225 31.9427C25.6763
      32.2455 25.683 31.257 24.5348 31.9939C24.4075 31.8915 24.4678 31.798 24.5415
      31.7178C24.8342 31.3639 25.2162 31.3104 25.6495 31.3305C23.5229 30.1483 22.5221
      32.7753 21.5392 31.4707C21.2443 31.5486 21.1326 31.8136 20.9472 32.0028C20.7864
      31.8269 20.9092 31.6154 20.9159 31.4084C20.7238 31.3193 20.4803 31.2748 20.5384
      30.9698C20.1609 30.8429 19.9017 31.0655 19.6225 31.277C19.3701 31.0833 19.7923
      30.7962 19.8705 30.5936C20.0961 30.2017 20.6076 30.5134 20.869 30.2329C21.6084
      29.8144 22.6405 30.4956 23.4849 30.3798C24.1372 30.4622 24.9436 29.7943 24.6152
      29.1309C23.9183 28.2448 24.0434 27.0849 24.0277 26.023C23.9406 25.4063 22.4528
      24.6182 22.0217 23.9503C21.49 23.3492 21.0745 22.6546 20.6613 21.9689C19.1646
      19.0815 19.6359 15.3702 17.7505 12.6876C16.8972 13.1573 15.7869 12.9347 15.0497
      12.3047C14.6521 12.6653 14.6342 13.1373 14.6029 13.6382C13.6513 12.6898 13.7697
      10.8954 14.5315 9.83794C14.842 9.4194 15.2128 9.07655 15.6305 8.776C15.7244
      8.70921 15.7556 8.6402 15.7534 8.53556C16.5062 5.15829 21.6442 5.8106 23.2704
      8.20162C24.4499 9.67319 24.8051 11.6212 26.141 12.9904C27.9393 14.9517 29.9878
      16.6793 31.6475 18.7698C33.2157 20.6799 34.3394 22.9173 35.3156 25.1748C35.7155
      25.9228 35.7177 26.8423 36.3097 27.4812C36.6023 27.8664 38.0253 28.9172 37.717
      29.289C37.8957 29.6741 39.2249 30.2084 38.7647 30.5824L38.7558 30.5735ZM63.7128
      19.0525L60.8199 21.9378C60.0492 22.7058 59.0954 23.2802 58.0633 23.5941L58.0119
      23.6097L57.9918 23.6609C57.6545 24.547 57.1452 25.3351 56.4728 26.0052L53.5776
      28.8905C52.3155 30.1483 50.6356 30.8407 48.8485 30.8407C47.0614 30.8407 45.3815
      30.1483 44.1194 28.8927C41.5124 26.2946 41.5124 22.0647 44.1194 19.4666L47.0145
      16.5813C47.7896 15.8088 48.7167 15.2545 49.7666 14.9317L49.818 14.9161L49.8381
      14.8649C50.1732 13.9788 50.687 13.1885 51.3639 12.5139L54.2568 9.62867C55.5189
      8.37082 57.1988 7.67845 58.9859 7.67845C60.773 7.67845 62.4551 8.37082 63.715
      9.62867C64.975 10.8865 65.6719 12.5607 65.6697 14.3417C65.6697 16.125 64.975
      17.7991 63.7128 19.0547V19.0525Z\" fill=\"currentColor\"/>\n<path d=\"M26.9839
      27.0806C26.7292 28.0668 26.6465 29.7477 25.3598 29.7966C25.2548 30.3666 25.7552
      30.5803 26.2132 30.3977C26.6644 30.1907 26.8789 30.5602 27.033 30.9298C27.73
      31.0322 28.7643 30.6983 28.8045 29.8723C27.7612 29.2735 27.4396 28.1336 26.9861
      27.0806H26.9839Z\" fill=\"currentColor\"/>\n</svg></div><h2 class=\"heading-style-h2\">Build
      your app with LangChain</h2><p class=\"text-size-medium\">Build context-aware,
      reasoning applications with LangChain\u2019s flexible framework that leverages
      your company\u2019s data and APIs. Future-proof your application by making vendor
      optionality part of your LLM infrastructure design.</p></div><a href=\"/langchain\"
      class=\"button is-link w-inline-block\"><div>Learn more about LangChain</div><div
      class=\"link_icon w-embed\"><svg width=\"15\" height=\"14\" viewBox=\"0 0 15
      14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path fill-rule=\"evenodd\"
      clip-rule=\"evenodd\" d=\"M14.0788 1.96304L2.33407 13.7077L0.919861 12.2935L12.6645
      0.548828L14.0788 1.96304Z\" fill=\"CurrentColor\"/>\n<path fill-rule=\"evenodd\"
      clip-rule=\"evenodd\" d=\"M12.5 2H1.5V0H14.5V13H12.5V2Z\" fill=\"CurrentColor\"/>\n</svg></div></a></div></div><div
      class=\"w-layout-grid product_component is-inverted\"><div id=\"w-node-_3077451b-9da5-5a12-4ade-d0cdb7f5d4ce-87f82a27\"
      class=\"product_text-wrapper\"><div class=\"product_text-content\"><div class=\"icon-embed-custom
      text-color-blue-500 w-embed\"><svg width=\"63\" height=\"33\" viewBox=\"0 0
      63 33\" fill=\"currentColor\" xmlns=\"http://www.w3.org/2000/svg\">\n<path fill-rule=\"evenodd\"
      clip-rule=\"evenodd\" d=\"M16.0556 0.580566H46.6516C55.3698 0.580566 62.4621
      7.69777 62.4621 16.4459C62.4621 25.194 55.3698 32.3112 46.6516 32.3112H16.0556C7.33738
      32.3112 0.245117 25.194 0.245117 16.4459C0.245117 7.69777 7.33738 0.580566 16.0556
      0.580566ZM30.103 25.1741C30.487 25.5781 31.0556 25.5581 31.5593 25.4534L31.5643
      25.4559C31.7981 25.2657 31.4658 25.0248 31.1484 24.7948C30.9581 24.6569 30.7731
      24.5228 30.7189 24.406C30.8946 24.1917 30.375 23.7053 29.9704 23.3266C29.8006
      23.1676 29.6511 23.0276 29.5818 22.9347C29.2939 22.6213 29.1782 22.226 29.0618
      21.8283C28.9846 21.5645 28.9071 21.2996 28.7788 21.0569C27.9883 19.2215 27.083
      17.401 25.8137 15.8474C24.9979 14.8148 24.0669 13.8901 23.1356 12.965C22.5352
      12.3687 21.9347 11.7722 21.3648 11.1466C20.7784 10.5413 20.4255 9.79548 20.072
      9.04847C19.7761 8.42309 19.4797 7.79685 19.0456 7.25139C17.7314 5.30625 13.5818
      4.77508 12.9733 7.52321C12.9758 7.60799 12.9484 7.66286 12.8735 7.71772C12.5369
      7.9646 12.2376 8.2439 11.9858 8.58306C11.3698 9.44341 11.275 10.9023 12.0431
      11.6753C12.0442 11.6587 12.0453 11.6422 12.0464 11.6257C12.0721 11.2354 12.0961
      10.8705 12.4047 10.5905C12.9982 11.1018 13.8985 11.2838 14.5868 10.9023C15.4166
      12.0926 15.681 13.5317 15.9462 14.9756C16.1671 16.1784 16.3887 17.3847 16.9384
      18.4534C16.9497 18.4723 16.9611 18.4912 16.9725 18.5101C17.2955 19.048 17.6238
      19.5946 18.0381 20.0643C18.1886 20.2976 18.4977 20.5495 18.8062 20.8009C19.2132
      21.1326 19.6194 21.4636 19.6591 21.7501C19.6609 21.8748 19.6603 22.0012 19.6598
      22.1283C19.6566 22.8808 19.6532 23.6601 20.1354 24.2788C20.4022 24.82 19.7489
      25.3636 19.2227 25.2963C18.9342 25.3363 18.619 25.2602 18.306 25.1848C17.8778
      25.0815 17.4537 24.9792 17.108 25.1766C17.011 25.2816 16.8716 25.2852 16.7316
      25.2889C16.5657 25.2933 16.3987 25.2977 16.3 25.4708C16.2797 25.5223 16.2323
      25.5804 16.183 25.6408C16.0748 25.7735 15.9575 25.9173 16.098 26.0269C16.1106
      26.0174 16.1231 26.0078 16.1356 25.9983C16.3484 25.8358 16.5513 25.681 16.8386
      25.7776C16.8004 25.9899 16.9375 26.0467 17.0745 26.1036C17.0984 26.1135 17.1224
      26.1234 17.1454 26.1342C17.1439 26.1835 17.1342 26.2332 17.1245 26.2825C17.1015
      26.4004 17.0789 26.516 17.1703 26.618C17.2137 26.5738 17.2521 26.5243 17.2905
      26.4746C17.3846 26.353 17.4791 26.2308 17.6491 26.1865C18.023 26.6858 18.3996
      26.4784 18.8721 26.2182C19.4051 25.9248 20.0601 25.5641 20.9708 26.0743C20.6217
      26.0569 20.3099 26.0993 20.0755 26.3885C20.0182 26.4534 19.9683 26.5282 20.0705
      26.613C20.6094 26.2639 20.8336 26.3893 21.0446 26.5074C21.1969 26.5927 21.3423
      26.6741 21.5942 26.5706C21.6538 26.5395 21.7133 26.5074 21.7729 26.4752C22.1775
      26.257 22.5877 26.0357 23.068 26.1117C22.7093 26.2152 22.5816 26.4426 22.4423
      26.6908C22.3734 26.8136 22.3017 26.9414 22.1977 27.0619C22.1429 27.1167 22.1179
      27.1815 22.1803 27.2738C22.9315 27.2114 23.2153 27.0209 23.5988 26.7636C23.7818
      26.6408 23.9875 26.5027 24.2775 26.3561C24.5981 26.1587 24.9187 26.285 25.2293
      26.4073C25.5664 26.54 25.8917 26.6681 26.1927 26.3736C26.2878 26.284 26.4071
      26.2829 26.5258 26.2818C26.569 26.2814 26.6122 26.281 26.6541 26.2763C26.5604
      25.7745 26.0319 25.7804 25.4955 25.7864C24.875 25.7933 24.2438 25.8004 24.2626
      25.022C24.8391 24.6282 24.8444 23.9449 24.8494 23.299C24.8507 23.1431 24.8518
      22.9893 24.8611 22.8424C25.2851 23.0788 25.7336 23.2636 26.1794 23.4473C26.5987
      23.62 27.0156 23.7917 27.4072 24.0045C27.8162 24.6628 28.4546 25.5357 29.305
      25.4783C29.3274 25.411 29.3474 25.3536 29.3723 25.2863C29.4213 25.2949 29.4731
      25.308 29.5257 25.3213C29.7489 25.3778 29.9879 25.4384 30.103 25.1741ZM46.7702
      17.6925C47.2625 18.1837 47.9304 18.4597 48.6267 18.4597C49.323 18.4597 49.9909
      18.1837 50.4832 17.6925C50.9756 17.2013 51.2523 16.5351 51.2523 15.8404C51.2523
      15.1458 50.9756 14.4795 50.4832 13.9883C49.9909 13.4971 49.323 13.2212 48.6267
      13.2212C48.3006 13.2212 47.9807 13.2817 47.6822 13.3965L46.1773 11.1999L45.1285
      11.9184L46.6412 14.1266C46.2297 14.6009 46.0011 15.2089 46.0011 15.8404C46.0011
      16.5351 46.2778 17.2013 46.7702 17.6925ZM42.0587 10.5787C42.4271 10.7607 42.8332
      10.8539 43.2443 10.8508C43.8053 10.8465 44.3501 10.663 44.7989 10.3274C45.2478
      9.99169 45.577 9.52143 45.7385 8.9855C45.9 8.44957 45.8851 7.87615 45.6961 7.34925C45.5072
      6.82235 45.154 6.36968 44.6884 6.05757C44.3471 5.82883 43.9568 5.68323 43.5488
      5.6325C43.1409 5.58176 42.7266 5.62731 42.3396 5.76548C41.9525 5.90365 41.6033
      6.13057 41.3202 6.42797C41.0371 6.72537 40.8279 7.08494 40.7096 7.47773C40.5913
      7.87051 40.567 8.28552 40.6389 8.68935C40.7107 9.09317 40.8766 9.47453 41.1233
      9.80269C41.3699 10.1309 41.6903 10.3967 42.0587 10.5787ZM42.0587 25.7882C42.4271
      25.9702 42.8332 26.0634 43.2443 26.0602C43.8053 26.0559 44.3501 25.8725 44.7989
      25.5368C45.2478 25.2011 45.577 24.7309 45.7385 24.195C45.9 23.659 45.8851 23.0856
      45.6961 22.5587C45.5072 22.0318 45.154 21.5791 44.6884 21.267C44.3471 21.0383
      43.9568 20.8927 43.5488 20.842C43.1409 20.7912 42.7266 20.8368 42.3396 20.9749C41.9525
      21.1131 41.6033 21.34 41.3202 21.6374C41.0371 21.9348 40.8279 22.2944 40.7096
      22.6872C40.5913 23.08 40.567 23.495 40.6389 23.8988C40.7107 24.3026 40.8766
      24.684 41.1233 25.0122C41.3699 25.3403 41.6903 25.6061 42.0587 25.7882ZM44.4725
      16.4916V15.1894H40.454C40.3529 14.7946 40.1601 14.4289 39.8911 14.1216L41.4029
      11.8819L40.3034 11.1526L38.7916 13.3924C38.5145 13.2923 38.2224 13.2395 37.9277
      13.2361C37.2333 13.2361 36.5675 13.5105 36.0765 13.9989C35.5856 14.4874 35.3097
      15.1498 35.3097 15.8405C35.3097 16.5313 35.5856 17.1937 36.0765 17.6821C36.5675
      18.1705 37.2333 18.4449 37.9277 18.4449C38.2224 18.4416 38.5145 18.3888 38.7916
      18.2887L40.3034 20.5284L41.3899 19.7992L39.8911 17.5594C40.1601 17.2522 40.3529
      16.8865 40.454 16.4916H44.4725Z\" fill=\"currentColor\"/>\n</svg></div><h2 class=\"heading-style-h2\">Run
      at scale with LangGraph\u00a0Cloud</h2><p class=\"text-size-medium\">Deploy
      your LangGraph app with LangGraph Cloud for fault-tolerant scalability - including
      support for async background jobs, built-in persistence, and distributed task
      queues.</p></div><a href=\"/langgraph\" class=\"button is-link w-inline-block\"><div>Learn
      more about LangGraph</div><div class=\"link_icon w-embed\"><svg width=\"15\"
      height=\"14\" viewBox=\"0 0 15 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path
      fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M14.0788 1.96304L2.33407 13.7077L0.919861
      12.2935L12.6645 0.548828L14.0788 1.96304Z\" fill=\"CurrentColor\"/>\n<path fill-rule=\"evenodd\"
      clip-rule=\"evenodd\" d=\"M12.5 2H1.5V0H14.5V13H12.5V2Z\" fill=\"CurrentColor\"/>\n</svg></div></a></div><div
      id=\"w-node-_3433c504-2da3-7d10-da26-74a85aa3cf13-87f82a27\" class=\"home_build-video-wrapper\"><img
      src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667c6d7284e58f4743a430e6_Langgraph%20UI-home-2.webp\"
      loading=\"lazy\" id=\"w-node-_756e1d1b-680d-0c4d-daa2-456dab276662-87f82a27\"
      sizes=\"100vw\" alt=\"\" srcset=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667c6d7284e58f4743a430e6_Langgraph%20UI-home-2-p-500.webp
      500w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667c6d7284e58f4743a430e6_Langgraph%20UI-home-2-p-800.webp
      800w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667c6d7284e58f4743a430e6_Langgraph%20UI-home-2-p-1080.webp
      1080w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667c6d7284e58f4743a430e6_Langgraph%20UI-home-2-p-1600.webp
      1600w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667c6d7284e58f4743a430e6_Langgraph%20UI-home-2.webp
      1768w\" class=\"product_langgraph-image-copy\"/></div></div><div class=\"w-layout-grid
      product_component is-home\"><div class=\"home_build-video-wrapper\"><div data-poster-url=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ce2a1ce257d3ea9e0be379_observe-animation-poster-00001.jpg\"
      data-video-urls=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ce2a1ce257d3ea9e0be379_observe-animation-transcode.mp4,https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ce2a1ce257d3ea9e0be379_observe-animation-transcode.webm\"
      data-autoplay=\"true\" data-loop=\"true\" data-wf-ignore=\"true\" class=\"product_bg-video
      w-background-video w-background-video-atom\"><video id=\"e48aa6f0-5db5-b5ce-1e0f-2150de4033cb-video\"
      autoplay=\"\" loop=\"\" style=\"background-image:url(&quot;https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ce2a1ce257d3ea9e0be379_observe-animation-poster-00001.jpg&quot;)\"
      muted=\"\" playsinline=\"\" data-wf-ignore=\"true\" data-object-fit=\"cover\"><source
      src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ce2a1ce257d3ea9e0be379_observe-animation-transcode.mp4\"
      data-wf-ignore=\"true\"/><source src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ce2a1ce257d3ea9e0be379_observe-animation-transcode.webm\"
      data-wf-ignore=\"true\"/></video></div></div><div class=\"product_text-wrapper\"><div
      class=\"product_text-content\"><div class=\"icon-embed-custom text-color-red-400
      w-embed\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"100%\" height=\"100%\"
      viewBox=\"0 0 77 39\" fill=\"none\" preserveAspectRatio=\"xMidYMid meet\" aria-hidden=\"true\"
      role=\"img\">\n<path d=\"M57.433 0H19.567C8.77775 0 0 8.7477 0 19.5C0 30.2523
      8.77775 39 19.567 39H57.433C68.2223 39 77 30.2523 77 19.5C77 8.7477 68.2223
      0 57.433 0ZM38.7563 30.5721C38.1321 30.7008 37.4297 30.7242 36.9523 30.2269C36.7761
      30.6306 36.3652 30.418 36.0483 30.3654C36.0189 30.4492 35.9935 30.5194 35.9661
      30.6013C34.9114 30.6715 34.1209 29.599 33.618 28.7898C32.6201 28.2535 31.4872
      27.924 30.4678 27.3604C30.4091 28.2886 30.6125 29.4391 29.7262 30.0378C29.6812
      31.822 32.4342 30.2484 32.6866 31.5783C32.491 31.5997 32.2738 31.5471 32.1173
      31.6973C31.3991 32.3934 30.5754 31.1707 29.7477 31.6758C28.6363 32.2335 28.5248
      32.6898 27.1492 32.8048C27.0729 32.6898 27.1042 32.6138 27.1688 32.5435C27.5562
      32.095 27.5836 31.5666 28.2449 31.3755C27.564 31.2682 26.9946 31.6426 26.4213
      31.939C25.6758 32.2432 25.6817 31.2526 24.535 31.9917C24.4079 31.8883 24.4685
      31.7947 24.5409 31.7148C24.8325 31.3599 25.216 31.3072 25.6484 31.3267C23.5215
      30.145 22.5196 32.7717 21.5374 31.4652C21.2419 31.5432 21.1304 31.8084 20.9445
      31.9956C20.7841 31.8201 20.9054 31.6075 20.9132 31.4008C20.7214 31.3111 20.4788
      31.2682 20.5356 30.9621C20.1599 30.8353 19.8977 31.0576 19.6179 31.2682C19.3654
      31.0732 19.7881 30.7885 19.8664 30.5858C20.0914 30.1957 20.604 30.5058 20.8643
      30.225C21.6039 29.8057 22.6351 30.4863 23.4804 30.3712C24.132 30.4531 24.9381
      29.7862 24.6114 29.1213C23.9148 28.234 24.038 27.0738 24.0224 26.013C23.9363
      25.3948 22.4472 24.607 22.0168 23.9401C21.4846 23.3395 21.0697 22.6434 20.6549
      21.959C19.158 19.071 19.6296 15.3601 17.7433 12.6789C16.8902 13.1488 15.7788
      12.9265 15.0431 12.2967C14.6459 12.6574 14.6283 13.1293 14.597 13.6305C13.6441
      12.6809 13.7634 10.8868 14.5246 9.82995C14.8357 9.41265 15.2075 9.06945 15.6223
      8.7672C15.7162 8.69895 15.7475 8.63265 15.7456 8.52735C16.4989 5.14995 21.6372
      5.80125 23.2612 8.1939C24.4392 9.66615 24.7953 11.6142 26.1317 12.9811C27.9299
      14.9428 29.9786 16.6705 31.6379 18.759C33.2071 20.668 34.3283 22.9066 35.3067
      25.1628C35.7058 25.9096 35.7098 26.83 36.3007 27.4696C36.5922 27.8557 38.0167
      28.9068 37.7075 29.2792C37.8856 29.6653 39.2162 30.1977 38.7544 30.5721H38.7563ZM40.7189
      14.4651L43.9181 11.1033C44.0922 10.92 44.2879 10.7542 44.4953 10.6099L48.1817
      8.05935C48.53 7.8195 49.0016 7.878 49.2775 8.19975L50.8703 10.0464C51.1344 10.3525
      51.1383 10.7991 50.8781 11.1091L48.759 13.6383C48.6905 13.7202 48.6631 13.8274
      48.6846 13.9308C48.6983 13.9971 48.7257 14.0536 48.7648 14.1004L51.1931 17.0176L49.4595
      18.8506L46.7494 15.7813C46.6986 15.7248 46.6281 15.6838 46.5518 15.6624C46.4462
      15.6351 46.3307 15.6526 46.2387 15.7111L43.4152 17.4583C43.0865 17.6611 42.6541
      17.6085 42.384 17.3296L40.7189 15.6019C40.4137 15.2841 40.4117 14.7849 40.715
      14.467L40.7189 14.4651ZM61.7123 29.8759L60.869 30.5838C60.7105 30.7164 60.5109
      30.7885 60.3055 30.7885C60.2565 30.7885 60.2057 30.7847 60.1548 30.7749L59.4562
      30.654C59.2508 30.6189 59.0629 30.5097 58.9318 30.3498L52.2439 22.2885L54.1751
      19.9953L61.7064 27.9006C61.8356 28.0371 61.9178 28.2145 61.9393 28.4017L62.0176
      29.1154C62.0489 29.404 61.9354 29.6887 61.7123 29.8779V29.8759ZM63.352 16.7817C63.1896
      16.9786 62.9274 17.0371 62.6985 16.924L58.3644 14.8298C58.2548 14.7752 58.1276
      14.7634 58.0102 14.7966C57.9418 14.8141 57.8811 14.8453 57.8263 14.8882L57.0808
      15.4713L44.5168 30.3946C44.3975 30.5389 44.2292 30.6442 44.0433 30.6871L43.7831
      30.7495C43.7166 30.7652 43.6481 30.7729 43.5796 30.7729C43.3741 30.7729 43.1726
      30.7008 43.0121 30.5662L41.8362 29.5776C41.6092 29.3865 41.4957 29.0998 41.529
      28.8073L41.5701 28.4524C41.5916 28.2633 41.6757 28.0839 41.8088 27.9454L55.0048
      13.9912L55.7209 13.0377C55.764 12.9792 55.7874 12.909 55.7874 12.8349C55.7874
      12.7452 55.7522 12.6613 55.6896 12.5989L53.2946 10.1848C52.9796 9.867 52.962
      9.35415 53.2516 9.01485L53.9794 8.1666C54.271 7.8273 54.7836 7.7649 55.1456
      8.0262L59.2469 10.9688C59.5365 11.1774 59.7928 11.427 60.01 11.7117L63.3618
      16.1109C63.5125 16.3098 63.5086 16.5828 63.3501 16.7758L63.352 16.7817Z\" fill=\"currentColor\"/>\n</svg></div><h2
      class=\"heading-style-h2\">Manage LLM performance with\u00a0LangSmith</h2><p
      class=\"text-size-medium\">Ship faster with LangSmith\u2019s debug, test, deploy,
      and monitoring workflows. Don\u2019t rely on \u201cvibes\u201d \u2013 add engineering
      rigor to your LLM-development workflow, whether you\u2019re building with LangChain
      or not.</p></div><a href=\"/langsmith\" class=\"button is-link w-inline-block\"><div>Learn
      more about LangSmith</div><div class=\"link_icon w-embed\"><svg width=\"15\"
      height=\"14\" viewBox=\"0 0 15 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path
      fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M14.0788 1.96304L2.33407 13.7077L0.919861
      12.2935L12.6645 0.548828L14.0788 1.96304Z\" fill=\"CurrentColor\"/>\n<path fill-rule=\"evenodd\"
      clip-rule=\"evenodd\" d=\"M12.5 2H1.5V0H14.5V13H12.5V2Z\" fill=\"CurrentColor\"/>\n</svg></div></a></div></div></div></div></div></section><div
      class=\"section_home-slider\"><section class=\"section_slider\"><div class=\"padding-global\"><div
      class=\"container-large\"><div class=\"happy-customers_heading\"><div id=\"w-node-de1f17ca-398b-87cf-f1fe-c1d40a2aa0f2-0a2aa0e8\"
      class=\"happy-customers_text-part\"><h3 class=\"heading-style-h2\">Hear from
      our happy customers</h3><p class=\"max-width-medium\">LangChain, LangGraph,
      and LangSmith help teams of all sizes, across all industries - from ambitious
      startups to established enterprises.</p></div></div><div class=\"happy-customers_slider-box\"><div
      class=\"swiper is-customers\"><div class=\"swiper-wrapper is-customers is-langgraph\"><div
      class=\"swiper-slide is-customers\"><div class=\"happy-customers_slide-text-part\"><div
      class=\"happy-customers_logotype-box\"><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65c5308aee06d9826765c897_Retool_logo%201.png\"
      loading=\"lazy\" width=\"77\" alt=\"\" class=\"happy-customers_logotype-img\"/></div><p
      class=\"text-size-regular is-opacity-80\">\u201cLangSmith helped us improve
      the accuracy and performance of Retool\u2019s fine-tuned models. Not only did
      we deliver a better product by iterating with LangSmith, but we\u2019re shipping
      new AI features to our users in a fraction of the time it would have taken without
      it.\u201d</p></div><div class=\"happy-customers_slide-author-part\"><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65c5308abdd2dbbdde5a94a1_Jamie%20Cuffe.png\"
      loading=\"lazy\" alt=\"\" class=\"happy-customers_slide-author-img\"/><div class=\"happy-customers_slide-author-text\"><div
      class=\"heading-style-h6\">Jamie Cuffe</div><div class=\"text-size-regular is-opacity-80\">Head
      of Self-Serve and New Products</div></div></div></div><div class=\"swiper-slide
      is-customers\"><div class=\"happy-customers_slide-text-part\"><div class=\"happy-customers_logotype-box\"><img
      src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65c5308a04d37cf7d3eb1341_Rakuten_Global_Brand_Logo.png\"
      loading=\"lazy\" alt=\"\" class=\"happy-customers_logotype-img\"/></div><p class=\"text-size-regular
      is-opacity-80\">\u201cBy combining the benefits of LangSmith and standing on
      the shoulders of a gigantic open-source community, we\u2019re able to identify
      the right approaches of using LLMs in an enterprise-setting faster.\u201d</p></div><div
      class=\"happy-customers_slide-author-part\"><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65c5308a8b6137d44c621cb4_Yusuke%20Kaji.png\"
      loading=\"lazy\" alt=\"\" class=\"happy-customers_slide-author-img\"/><div class=\"happy-customers_slide-author-text\"><div
      class=\"heading-style-h6\">Yusuke Kaji</div><div class=\"text-size-regular is-opacity-80\">General
      Manager of AI</div></div></div></div><div class=\"swiper-slide is-customers\"><div
      class=\"happy-customers_slide-text-part\"><div class=\"happy-customers_logotype-box
      is-no-padding\"><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65c5308aea1371b447cc4af9_elastic-ar21.png\"
      loading=\"lazy\" alt=\"\" class=\"happy-customers_logotype-img is--bigger\"/></div><p
      class=\"text-size-regular is-opacity-80\">\u201cWorking with LangChain and LangSmith
      on the Elastic AI Assistant had a significant positive impact on the overall
      pace and quality of the development and shipping experience. We couldn\u2019t
      have achieved \u00a0the product experience delivered to our customers without
      LangChain, and we couldn\u2019t have done it at the same pace without LangSmith.\u201d</p></div><div
      class=\"happy-customers_slide-author-part\"><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65c5308a4095d5a871de7479_James%20Spiteri.png\"
      loading=\"lazy\" alt=\"\" class=\"happy-customers_slide-author-img\"/><div class=\"happy-customers_slide-author-text\"><div
      class=\"heading-style-h6\">James Spiteri</div><div class=\"text-size-regular
      is-opacity-80\">Director of Security Products</div></div></div></div><div class=\"swiper-slide
      is-customers\"><div class=\"happy-customers_slide-text-part\"><div class=\"happy-customers_logotype-box\"><img
      src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65c530539f4824b828357352_Logo_de_Fintual%201.png\"
      loading=\"lazy\" width=\"88\" alt=\"\" class=\"happy-customers_logotype-img\"/></div><p
      class=\"text-size-regular is-opacity-80\">\u201cAs soon as we heard about LangSmith,
      we moved our entire development stack onto it. We could have built evaluation,
      testing and monitoring tools in house, but with LangSmith it took us 10x less
      time to get a 1000x better tool.\u201d</p></div><div class=\"happy-customers_slide-author-part\"><img
      src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65c53058acbff86f4c2dcee2_jose%20pena.png\"
      loading=\"lazy\" alt=\"\" class=\"happy-customers_slide-author-img\"/><div class=\"happy-customers_slide-author-text\"><div
      class=\"heading-style-h6\">Jose Pe\u00f1a</div><div class=\"text-size-regular
      is-opacity-80\">Senior Manager</div></div></div></div></div></div></div><div
      id=\"w-node-de1f17ca-398b-87cf-f1fe-c1d40a2aa0ec-0a2aa0e8\" class=\"happy-customers_navigation-part\"><div
      class=\"happy-customers_navigation-group\"><div class=\"single-arrow is-prev\"><div
      class=\"icon-embed-custom is--arrow w-embed\"><svg xmlns=\"http://www.w3.org/2000/svg\"
      width=\"100%\" height=\"100%\" viewBox=\"0 0 19 19\" fill=\"none\" preserveAspectRatio=\"xMidYMid
      meet\" aria-hidden=\"true\" role=\"img\">\n<path d=\"M18.3946 9.42475L1.7851
      9.42475\" stroke=\"currentColor\" stroke-width=\"2\"/>\n<path d=\"M10 18L1.51472
      9.51472L10 1.02944\" stroke=\"currentColor\" stroke-width=\"2\"/>\n</svg></div></div><div
      class=\"single-arrow is-next\"><div class=\"icon-embed-custom is--arrow w-embed\"><svg
      xmlns=\"http://www.w3.org/2000/svg\" width=\"100%\" height=\"100%\" viewBox=\"0
      0 19 19\" fill=\"none\" preserveAspectRatio=\"xMidYMid meet\" aria-hidden=\"true\"
      role=\"img\">\n<path d=\"M0.605412 9.42475L17.2149 9.42475\" stroke=\"currentColor\"
      stroke-width=\"2\"/>\n<path d=\"M9 18L17.4853 9.51472L9 1.02944\" stroke=\"currentColor\"
      stroke-width=\"2\"/>\n</svg></div></div></div></div></div><div class=\"hide
      w-embed w-script\"><link\n  rel=\"stylesheet\"\n  href=\"https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css\"\n/>\n<script
      src=\"https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js\"></script>\n<script>\nvar
      Webflow = Webflow || [];\nWebflow.push(function () {\n  // DOMready has fired\n  //
      May now use jQuery and Webflow api\n\n\nconst teamSlider = new Swiper(\".swiper.is-customers\",
      {\n  // Parameters\n  loop: true,\n  speed: 700,\n\tloopAdditionalSlides: 5,\n  grabCursor:
      true,\n  slidesPerView: 1.2,\n  spaceBetween: 24,\n  allowTouchMove: true,\n\n  //
      Navigation arrows\n  navigation: {\n    nextEl: \".single-arrow.is-next\",\n    prevEl:
      \".single-arrow.is-prev\"\n  },\n  autoplay: {\n    delay: 5000,\n    disableOnInteraction:
      false,\n    pauseOnMouseEnter: true,\n  },\n\n  // Responsive breakpoints\n  breakpoints:
      {\n    // when window width is >= 480px\n    860: {\n      slidesPerView: 2,\n      spaceBetween:
      30\n    },\n    // when window width is >= 1200px\n    1200: {\n      slidesPerView:
      3,\n      spaceBetween: 40\n    }\n  }\n});\n\n});\n</script>\n\n<style>\n @media
      screen and (max-width:991px) {.swiper-slide {\nheight: auto;\n}}\n</style></div></div></section></div><section
      class=\"section_reference background-color-green400\"><div class=\"padding-global\"><div
      class=\"container-xlarge\"><div data-w-id=\"00671cb6-faa2-aff9-f3ca-4d15160aa934\"
      class=\"w-layout-grid reference_component is-horizontal-tablet\"><div class=\"max-width-medium-plus
      is-padding-top\"><h2 class=\"heading-style-h2 margin-bottom margin-xsmall\">The
      reference architecture enterprises adopt for success.</h2><p class=\"text-size-medium
      max-width-small\">LangChain\u2019s suite of products can be used independently
      or stacked together for multiplicative impact \u2013 guiding you through building,
      running, and managing your LLM apps.</p></div><div class=\"architecture-image_wrapper\"><img
      src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/6695b116b0b60c78fd4ef462_15.07.24%20-Updated%20stack%20diagram%20-%20lightfor%20website-3.webp\"
      loading=\"lazy\" height=\"Auto\" alt=\"\" width=\"71\" class=\"fullwidth-image
      is-desktop\"/><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667d392696fc0bc3e17a6d04_New%20LC%20stack%20-%20light-2.webp\"
      loading=\"lazy\" width=\"623\" sizes=\"(max-width: 479px) 93vw, (max-width:
      767px) 96vw, (max-width: 991px) 45vw, 100vw\" alt=\"\" srcset=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667d392696fc0bc3e17a6d04_New%20LC%20stack%20-%20light-2-p-500.webp
      500w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/667d392696fc0bc3e17a6d04_New%20LC%20stack%20-%20light-2.webp
      833w\" class=\"fullwidth-image is-mobile\"/></div></div></div></div></section><section
      class=\"section_community\"><div class=\"padding-global\"><div class=\"container-xlarge\"><div
      data-w-id=\"43123052-e7a7-8a89-24f2-e32f8cae37e1\" class=\"w-layout-grid community_component
      is-inverted\"><div id=\"w-node-_53103223-f4d4-f3e9-d36a-ce593a1a793c-87f82a27\"><div
      class=\"community_bubbles-row\"><div class=\"community_bubble\"><div class=\"heading-style-decorative-2
      is-numeric\">15M+</div><div class=\"heading-style-h6\">Monthly Downloads</div></div><div
      class=\"community_bubble is-right\"><div class=\"heading-style-decorative-2
      is-numeric\">100K+</div><div class=\"heading-style-h6\">Apps Powered</div></div></div><div
      class=\"community_bubbles-row is-bottom\"><div class=\"community_bubble\"><div
      class=\"heading-style-decorative-2 is-numeric\">75K+</div><div class=\"heading-style-h6\">GitHub
      Stars</div></div><div class=\"community_bubble is-right\"><div class=\"heading-style-decorative-2
      is-numeric\">3K+</div><div class=\"heading-style-h6\">Contributors</div></div></div></div><div
      class=\"product_text-wrapper\"><div class=\"product_text-content\"><h2 class=\"heading-style-h2\">The
      biggest developer community in GenAI</h2><p class=\"text-size-medium\">Learn
      alongside the 1M+ developers who are pushing the industry forward.</p></div><a
      href=\"/langchain\" class=\"button is-link w-inline-block\"><div>Explore LangChain</div><div
      class=\"link_icon w-embed\"><svg width=\"15\" height=\"14\" viewBox=\"0 0 15
      14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path fill-rule=\"evenodd\"
      clip-rule=\"evenodd\" d=\"M14.0788 1.96304L2.33407 13.7077L0.919861 12.2935L12.6645
      0.548828L14.0788 1.96304Z\" fill=\"CurrentColor\"/>\n<path fill-rule=\"evenodd\"
      clip-rule=\"evenodd\" d=\"M12.5 2H1.5V0H14.5V13H12.5V2Z\" fill=\"CurrentColor\"/>\n</svg></div></a></div></div></div></div></section><section
      class=\"section_home-cta\"><div class=\"padding-global is-wide\"><div class=\"container-xlarge\"><div
      class=\"cta-block_bg-content\"><div data-w-id=\"6e007325-e41b-6cec-6218-6942fd9199ed\"
      style=\"opacity:0\" class=\"cta-block_text-part\"><div class=\"product_text-content\"><h2
      class=\"heading-style-h2 text-align-center\">Get started with the LangSmith
      platform today</h2></div><div class=\"cta-block_buttons-wrap\"><a href=\"/contact-sales\"
      class=\"button w-button\">Get a demo</a><a href=\"https://smith.langchain.com/\"
      target=\"_blank\" class=\"button is-alternate is-fill-green w-button\">Sign
      up for free</a></div></div><img src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ccf12801bc39bf912a58f3_Home%20C.webp\"
      loading=\"lazy\" width=\"920\" sizes=\"(max-width: 479px) 95vw, (max-width:
      767px) 97vw, (max-width: 991px) 98vw, (max-width: 1439px) 95vw, (max-width:
      1919px) 96vw, 1808px\" alt=\"\" srcset=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ccf12801bc39bf912a58f3_Home%20C-p-500.webp
      500w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ccf12801bc39bf912a58f3_Home%20C-p-800.webp
      800w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ccf12801bc39bf912a58f3_Home%20C-p-1080.webp
      1080w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ccf12801bc39bf912a58f3_Home%20C-p-1600.webp
      1600w, https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65ccf12801bc39bf912a58f3_Home%20C.webp
      1840w\" class=\"home_cta-bg-image\"/></div></div></div></section><header id=\"blog-header-1\"
      class=\"section_home-inspiration\"><div class=\"padding-global is-inspo\"><div
      class=\"container-xlarge\"><div data-w-id=\"f49f52e7-0f65-8bf9-79c6-b74cb4e015a5\"
      class=\"inspo_component\"><div class=\"w-layout-grid layout_component is-bigger-gap\"><div
      class=\"layout_content-col\"><p class=\"margin-bottom margin-medium ishomepage\">Teams
      building with LangChain are driving operational efficiency, increasing discovery
      &amp; personalization, and delivering premium products that generate revenue.</p><a
      href=\"/use-cases\" class=\"button is-link w-inline-block\"><div>Discover Use
      Cases</div><div class=\"link_icon w-embed\"><svg width=\"15\" height=\"14\"
      viewBox=\"0 0 15 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path
      fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M14.0788 1.96304L2.33407 13.7077L0.919861
      12.2935L12.6645 0.548828L14.0788 1.96304Z\" fill=\"CurrentColor\"/>\n<path fill-rule=\"evenodd\"
      clip-rule=\"evenodd\" d=\"M12.5 2H1.5V0H14.5V13H12.5V2Z\" fill=\"CurrentColor\"/>\n</svg></div></a></div><div
      class=\"layout_content-right\"><h2>Get inspired by companies who have done it.</h2></div></div><div
      class=\"use-case_list-wrapper\"><div class=\"w-layout-grid use-case_list\"><a
      data-w-id=\"f49f52e7-0f65-8bf9-79c6-b74cb4e015a8\" style=\"opacity:0\" href=\"https://blog.langchain.dev/ally-financial-collaborates-with-langchain-to-deliver-critical-coding-module-to-mask-personal-identifying-information-in-a-compliant-and-safe-manner/\"
      target=\"_blank\" class=\"use-case_item is-alternate w-inline-block\"><div class=\"use-case_image-link\"><div
      class=\"use-case_image-wrapper\"><div class=\"use-case_image is-alternate\"><img
      alt=\"\" loading=\"lazy\" width=\"88.5\" src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65bcd7ee85507bdf350399c3_Ally_Financial%201.svg\"/></div></div></div><div
      class=\"use-case_link-wrap\"><div class=\"use-case_pill\">Financial Services</div><div><div
      class=\"icon-embed-medium is-use-case w-embed\"><svg xmlns=\"http://www.w3.org/2000/svg\"
      width=\"100%\" height=\"100%\" viewBox=\"0 0 42 42\" fill=\"none\" preserveAspectRatio=\"xMidYMid
      meet\" aria-hidden=\"true\" role=\"img\">\n<path d=\"M1.42187 41.0005L40.5708
      1.85156\" stroke=\"currentColor\" stroke-width=\"2\"/>\n<path d=\"M1 0.999512L41
      0.999512V40.9995\" stroke=\"currentColor\" stroke-width=\"2\"/>\n</svg></div></div></div></a><a
      data-w-id=\"0b88affc-7742-f857-71d8-************\" style=\"opacity:0\" href=\"https://blog.langchain.dev/llms-accelerate-adyens-support-team-through-smart-ticket-routing-and-support-agent-copilot/\"
      target=\"_blank\" class=\"use-case_item is-alternate w-inline-block\"><div class=\"use-case_image-link\"><div
      class=\"use-case_image-wrapper\"><div class=\"use-case_image is-alternate\"><img
      width=\"136\" loading=\"lazy\" src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65bcd8b3ae4dc901daa3037a_Adyen_Corporate_Logo%201.svg\"
      alt=\"\"/></div></div></div><div class=\"use-case_link-wrap\"><div class=\"use-case_pill\">FinTech</div><div><div
      class=\"icon-embed-medium is-use-case w-embed\"><svg xmlns=\"http://www.w3.org/2000/svg\"
      width=\"100%\" height=\"100%\" viewBox=\"0 0 42 42\" fill=\"none\" preserveAspectRatio=\"xMidYMid
      meet\" aria-hidden=\"true\" role=\"img\">\n<path d=\"M1.42187 41.0005L40.5708
      1.85156\" stroke=\"currentColor\" stroke-width=\"2\"/>\n<path d=\"M1 0.999512L41
      0.999512V40.9995\" stroke=\"currentColor\" stroke-width=\"2\"/>\n</svg></div></div></div></a><a
      data-w-id=\"a310b82f-3112-9979-5481-ecce5be7c63f\" style=\"opacity:0\" href=\"https://blog.langchain.dev/langchain-partners-with-elastic-to-launch-the-elastic-ai-assistant/\"
      target=\"_blank\" class=\"use-case_item is-alternate w-inline-block\"><div class=\"use-case_image-link\"><div
      class=\"use-case_image-wrapper\"><div class=\"use-case_image is-alternate\"><img
      width=\"280\" loading=\"lazy\" src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65c534b3fa387379c0f4ebff_elastic-ar21%20(1).png\"
      alt=\"\" class=\"inspo_image is-elastic\"/></div></div></div><div class=\"use-case_link-wrap\"><div
      class=\"use-case_pill\">Technology</div><div><div class=\"icon-embed-medium
      is-use-case w-embed\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"100%\"
      height=\"100%\" viewBox=\"0 0 42 42\" fill=\"none\" preserveAspectRatio=\"xMidYMid
      meet\" aria-hidden=\"true\" role=\"img\">\n<path d=\"M1.42187 41.0005L40.5708
      1.85156\" stroke=\"currentColor\" stroke-width=\"2\"/>\n<path d=\"M1 0.999512L41
      0.999512V40.9995\" stroke=\"currentColor\" stroke-width=\"2\"/>\n</svg></div></div></div></a></div></div></div></div></div></header><section
      class=\"section_home-langsmith\"><div class=\"padding-global\"><div class=\"container-xlarge\"><div
      class=\"w-layout-grid reference_component\"><div id=\"w-node-_87e307f2-a5bc-2c2f-8881-41eb8462a4d7-87f82a27\"
      class=\"flex-left\"><h2 class=\"heading-style-h2 margin-medium margin-bottom
      is-homepage\">LangSmith is the enterprise\u00a0DevOps platform\u00a0built for
      LLMs.</h2><a href=\"/langsmith\" target=\"_blank\" class=\"button is-link w-inline-block\"><div>Explore
      LangSmith</div><div class=\"link_icon w-embed\"><svg width=\"15\" height=\"14\"
      viewBox=\"0 0 15 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path
      fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M14.0788 1.96304L2.33407 13.7077L0.919861
      12.2935L12.6645 0.548828L14.0788 1.96304Z\" fill=\"CurrentColor\"/>\n<path fill-rule=\"evenodd\"
      clip-rule=\"evenodd\" d=\"M12.5 2H1.5V0H14.5V13H12.5V2Z\" fill=\"CurrentColor\"/>\n</svg></div></a></div><div><div
      class=\"home_langsmith-single\"><div class=\"home_langsmith-dot w-embed\"><svg
      xmlns=\"http://www.w3.org/2000/svg\" width=\"100%\" height=\"100%\" viewBox=\"0
      0 8 8\" fill=\"none\" preserveAspectRatio=\"xMidYMid meet\" aria-hidden=\"true\"
      role=\"img\">\n<circle cx=\"4\" cy=\"4\" r=\"4\" fill=\"currentColor\"/>\n</svg></div><div
      class=\"heading-style-h5\">Gain visibility to make trade offs between cost,
      latency, and quality.</div></div><div class=\"home_langsmith-single\"><div class=\"home_langsmith-dot
      w-embed\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"100%\" height=\"100%\"
      viewBox=\"0 0 8 8\" fill=\"none\" preserveAspectRatio=\"xMidYMid meet\" aria-hidden=\"true\"
      role=\"img\">\n<circle cx=\"4\" cy=\"4\" r=\"4\" fill=\"currentColor\"/>\n</svg></div><div
      class=\"heading-style-h5\">Increase developer productivity.</div></div><div
      class=\"home_langsmith-single\"><div class=\"home_langsmith-dot w-embed\"><svg
      xmlns=\"http://www.w3.org/2000/svg\" width=\"100%\" height=\"100%\" viewBox=\"0
      0 8 8\" fill=\"none\" preserveAspectRatio=\"xMidYMid meet\" aria-hidden=\"true\"
      role=\"img\">\n<circle cx=\"4\" cy=\"4\" r=\"4\" fill=\"currentColor\"/>\n</svg></div><div
      class=\"heading-style-h5\">Eliminate manual, error-prone testing.</div></div><div
      class=\"home_langsmith-single\"><div class=\"home_langsmith-dot w-embed\"><svg
      xmlns=\"http://www.w3.org/2000/svg\" width=\"100%\" height=\"100%\" viewBox=\"0
      0 8 8\" fill=\"none\" preserveAspectRatio=\"xMidYMid meet\" aria-hidden=\"true\"
      role=\"img\">\n<circle cx=\"4\" cy=\"4\" r=\"4\" fill=\"currentColor\"/>\n</svg></div><div
      class=\"heading-style-h5\">Reduce hallucinations and improve reliability.</div></div><div
      class=\"home_langsmith-single is-last\"><div class=\"home_langsmith-dot w-embed\"><svg
      xmlns=\"http://www.w3.org/2000/svg\" width=\"100%\" height=\"100%\" viewBox=\"0
      0 8 8\" fill=\"none\" preserveAspectRatio=\"xMidYMid meet\" aria-hidden=\"true\"
      role=\"img\">\n<circle cx=\"4\" cy=\"4\" r=\"4\" fill=\"currentColor\"/>\n</svg></div><div
      class=\"heading-style-h5\">Enterprise deployment options to keep data secure.</div></div></div></div></div></div></section><section
      class=\"section_bottom-cta\"><div class=\"padding-global is-wide\"><div class=\"container-xlarge\"><div
      class=\"padding-section-main is-join-us\"><div class=\"w-layout-grid join-us_component\"><div
      class=\"layout_content-right max-width-medium\"><h2 class=\"margin-small margin-bottom\">Ready
      to start shipping \u2028reliable GenAI apps faster?</h2><p class=\"text-size-medium\">Get
      started with LangChain, LangGraph, and LangSmith to enhance your LLM app development,
      from prototype to production.</p><div class=\"join-us_buttons-wrapper is-centered\"><a
      href=\"/contact-sales\" class=\"button w-button\">Get a demo</a><a href=\"https://smith.langchain.com/\"
      target=\"_blank\" class=\"button is-secondary w-button\">Sign up for free</a></div></div></div></div></div></div></section></main><footer
      class=\"footer_component\"><div class=\"padding-global\"><div class=\"footer_top-component\"><div
      class=\"w-layout-grid footer_top-wrapper\"><div class=\"w-layout-grid footer_left-wrapper\"><div
      class=\"footer_link-list\"><div class=\"margin-bottom margin-small\"><div class=\"heading-style-h6
      is-footer\">Products</div></div><div id=\"w-node-ed59fd8f-4548-9b12-643e-b1816711f5bf-6711f5b5\"
      class=\"footer-link_column\"><a href=\"/langchain\" class=\"footer_link\">LangChain</a><a
      href=\"/langsmith\" class=\"footer_link\">LangSmith</a><a href=\"/langgraph\"
      class=\"footer_link\">LangGraph</a><a href=\"/agents\" class=\"footer_link\">Agents</a><a
      href=\"/evaluation\" class=\"footer_link\">Evaluation</a><a href=\"/retrieval\"
      class=\"footer_link\">Retrieval</a></div></div><div class=\"footer_link-list\"><div
      class=\"margin-bottom margin-small\"><div class=\"heading-style-h6 is-footer\">Resources</div></div><div
      class=\"footer-link_column is-resources\"><a href=\"https://python.langchain.com/\"
      target=\"_blank\" class=\"footer_link\">Python Docs</a><a href=\"https://js.langchain.com/docs/get_started/introduction/\"
      target=\"_blank\" class=\"footer_link\">JS/TS Docs</a><a href=\"https://github.com/langchain-ai\"
      target=\"_blank\" class=\"footer_link link\">GitHub</a><a href=\"https://python.langchain.com/v0.2/docs/integrations/platforms/\"
      target=\"_blank\" class=\"footer_link\">Integrations</a><a href=\"https://changelog.langchain.com/\"
      target=\"_blank\" class=\"footer_link\">Changelog</a><a href=\"https://trust.langchain.com/\"
      target=\"_blank\" class=\"footer_link\">LangSmith Trust Portal</a></div></div><div
      class=\"footer_link-list\"><div class=\"margin-bottom margin-small\"><div class=\"heading-style-h6
      is-footer\">Company</div></div><div class=\"footer-link_column\"><a href=\"/about\"
      class=\"footer_link\">About</a><a href=\"https://blog.langchain.dev/\" target=\"_blank\"
      class=\"footer_link\">Blog</a><a href=\"https://twitter.com/LangChainAI\" target=\"_blank\"
      class=\"footer_link\">Twitter</a><a href=\"https://www.linkedin.com/company/langchain/\"
      target=\"_blank\" class=\"footer_link\">LinkedIn</a><a href=\"https://www.youtube.com/@LangChain\"
      target=\"_blank\" class=\"footer_link\">YouTube</a><a href=\"/join-community\"
      class=\"footer_link\">Community</a><a href=\"https://drive.google.com/drive/folders/17xybjzmVBdsQA-VxouuGLxF6bDsHDe80?usp=sharing\"
      target=\"_blank\" class=\"footer_link\">Marketing Assets</a></div></div></div><div
      id=\"w-node-ed59fd8f-4548-9b12-643e-b1816711f5ee-6711f5b5\" class=\"footer_right-wrapper\"><div
      class=\"margin-bottom margin-xsmall\"><div class=\"footer__form-title\"><div
      class=\"heading-style-h6\">Sign up for our newsletter to stay up to date</div></div></div><div
      class=\"footer_form-block w-form\"><form id=\"wf-form-footer_form\" name=\"wf-form-footer_form\"
      data-name=\"footer_form\" action=\"https://langchain.us11.list-manage.com/subscribe/post?u=4ad47fb6a8889d6654064ff1b&amp;amp;id=6bc1ef8ac4&amp;amp;f_id=0072b7e0f0\"
      method=\"post\" class=\"footer_form\" data-wf-page-id=\"660189f45f9033dd87f82a27\"
      data-wf-element-id=\"ed59fd8f-4548-9b12-643e-b1816711f5f5\"><input class=\"form_input
      w-input\" maxlength=\"256\" name=\"EMAIL\" data-name=\"EMAIL\" placeholder=\"Your
      email...\" type=\"email\" id=\"EMAIL\" required=\"\"/><input type=\"submit\"
      data-wait=\"Please wait...\" aria-label=\"Subscribe\" class=\"news_button w-button\"
      value=\"\"/></form><div class=\"form_success is-footer w-form-done\"><div>Thank
      you! Your submission has been received!</div></div><div class=\"form_error is-news
      w-form-fail\"><div>Oops! Something went wrong while submitting the form.</div></div></div></div></div></div><img
      src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/65c6a38f9c53ec71f5fc73de_langchain-word.svg\"
      loading=\"lazy\" alt=\"\" class=\"footer_logo\"/></div><div class=\"padding-global
      is-bottom-footer\"><div class=\"footer_bottom-component\"><div class=\"padding-vertical\"><div
      class=\"footer_bottom\"><a href=\"https://status.smith.langchain.com/\" target=\"_blank\"
      class=\"footer_link is-status w-inline-block\"><div class=\"status-dot\"></div><div>All
      systems <span class=\"system-status\">operational</span></div></a><a href=\"/privacy-policy\"
      class=\"footer_link\">Privacy Policy</a><a href=\"/terms-of-service\" class=\"footer_link\">Terms
      of Service</a><div class=\"hide w-embed w-script\"><script type=\"text/javascript\"
      src=\"https://cdn.statuspage.io/se-v2.js\"></script>\n<script>\n  var sp = new
      StatusPage.page({ page : ''nl3597xjtlm2'' });\n  sp.summary({\n    success :
      function(data) {\n      // Get the status value from the first component\n      var
      status = data.components[0].status;\n      \n      // Log the status to the
      console (optional, you can remove this line)\n      console.log(status);\n      \n      //
      Find the span with the class ''.system-status'' and set its content\n      var
      statusSpan = document.querySelector(''.system-status'');\n      if(statusSpan)
      {\n        statusSpan.textContent = status; // or use innerHTML if you need
      to include HTML\n      }\n    }\n  });\n</script></div></div></div></div></div></footer></div><script
      src=\"https://d3e54v103j8qbb.cloudfront.net/js/jquery-3.5.1.min.dc5e7f18c8.js?site=65b8cd72835ceeacd4449a53\"
      type=\"text/javascript\" integrity=\"sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0=\"
      crossorigin=\"anonymous\"></script><script src=\"https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/js/langchain-tonik.1edf28489.js\"
      type=\"text/javascript\"></script><script>\n\nvar Webflow = Webflow || [];\nWebflow.push(function
      () {\n  // DOMready has fired\n  // May now use jQuery and Webflow api\ndocument.querySelector(''.w-nav-button'').addEventListener(''click'',
      function() {\n    var logo = document.querySelector(''.navbar_logo-svg'');\n    \n    //
      Toggle ''open'' class for both nav and logo\n    logo.classList.toggle(''is-light'');\n});\n});\n\n</script>\n\n<script>\n//
      Debounce function to limit the rate at which a function is executed\nfunction
      debounce(func, wait) {\n    let timeout;\n    return function executedFunction(...args)
      {\n        const later = () => {\n            clearTimeout(timeout);\n            func(...args);\n        };\n        clearTimeout(timeout);\n        timeout
      = setTimeout(later, wait);\n    };\n}\n\n// Function to update video sources\nfunction
      updateVideoSources() {\n    const videos = document.querySelectorAll(''video'');
      // Select all video elements\n    videos.forEach(video => {\n        const sources
      = video.querySelectorAll(''source[data-src]''); // Select all source elements
      with a \"data-src\" attribute within each video\n        if (window.innerWidth
      > 990) {\n            sources.forEach(source => {\n                source.src
      = source.getAttribute(''data-src''); // If screen width > 990, set src to data-src\n            });\n        }
      else {\n            sources.forEach(source => {\n                source.src
      = ''#''; \n            });\n        }\n        video.load(); // Important: load
      the new source(s) into the video\n    });\n}\n\n// Event listener for DOMContentLoaded
      to run once on start\ndocument.addEventListener(''DOMContentLoaded'', function()
      {\n    updateVideoSources();\n});\n\n// Event listener for resize with debounce\nwindow.addEventListener(''resize'',
      debounce(updateVideoSources, 250)); // Debounce resize event\n\n</script>\n\n<script
      type=\"text/javascript\">\n\t_linkedin_partner_id = \"5973154\";\n\twindow._linkedin_data_partner_ids
      = window._linkedin_data_partner_ids || [];\n\twindow._linkedin_data_partner_ids.push(_linkedin_partner_id);\n</script>\n\n<script
      type=\"text/javascript\">\n\t(function(l) {\n\t\tif (!l){window.lintrk = function(a,b){window.lintrk.q.push([a,b])};\t\n\t\twindow.lintrk.q=[]}\n\tvar
      s = document.getElementsByTagName(\"script\")[0];\n\tvar b = document.createElement(\"script\");\n\tb.type
      = \"text/javascript\";b.async = true;\n\tb.src = \"https://snap.licdn.com/li.lms-analytics/insight.min.js\";\n\ts.parentNode.insertBefore(b,
      s);})(window.lintrk);\n</script>\n\n<noscript>\n\t<img height=\"1\" width=\"1\"
      style=\"display:none;\" alt=\"\" src=\"https://px.ads.linkedin.com/collect/?pid=5973154&fmt=gif\"
      />\n</noscript></body></html>\n</Website content>"}], "model": "claude-3-5-sonnet-20240620"}'
    headers: {}
    method: POST
    uri: https://api.anthropic.com/v1/messages
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA3xUTW8bOQz9K4Qu2YUnhu00iTu3Iui2QJJDux+XZhFoRpwZwRpyKlL1eoP894Vk
        O3FQYE8SRPFR7/FRT8Y7U5tR+sfF8qb/3q7vrlJz01z+9dv6y/WXj9cf2FRGdxPmWyhiezSViRzy
        gRXxopbUVGZkh8HUpg02OTy/OL88FyZCPV8tVu8WV6uFqUzLpEhq6m9PR1DFf3J6WWrzGSOCjQjC
        I8IGd0CsKGAbTgo6INxZ6m8G6wm22IhXhMYKOmAq4UOF+oEeaDk/uc1dh1HAgqScxB1MkV1qVaDj
        CE3ywXnqK4iJqGwsORgt2d5TD3d392CnKfjWqmeS+oEA4Py1QA0foIt2xC3HzRtI2HodMoCc5HyK
        dhr+JyfziByCbQKC7ZHUt5CvdYG3p0C/j14L0BSsdhzHguOwSX1faCiKlo3DKfAugxdmTF45nnCT
        rNlqDn8M+KLt4Psh+H5QAR2snuj5ol5rCRqElJvgyeGE5JA07IAjKPeoA8Z5xr6Ywy3uQNSqwJgp
        MaE7Krm8vJ/lV+kQduB4S4GtOzJdLha3s/JImHiLEd0hcH15O4NPXj+nJgPHY8LF7WyvoW+Scizc
        3u25FWI4ToMV/y/KK6czgWBjj+DwBwaeMELL45jI6w5+Wd7PXgPya2F0WRAPli1CZ1ltEOgijzl7
        suRRIPgNwldU5lDBV7tJirR32MdgJfd27/AkpSE/qVyqXb3tzcHREYVTbI9FHLcpa1tsWh2lCZ42
        UkET+GDsV2KT7XGPf31gc5bHhHArAVUxgvie0gTFXH4/Zh2zHrq6PlF1ijyWeW1YB+gi4jE510RS
        jFP0khUeeV/0/Rz+FITWSh7zNrJItlESjS/CdZ4std4GEIw/fItS5TPFdtizyTviwP2udOLFtehK
        jeXirXIO86uyZTPTwJq/gz4iErQcylBUMFpthzfdOBNooqU8n3Pz/HdlRHl6jGiFydQGyT1qimQO
        AcHvCalFU1MKoTKpfJ71k/E0JX1U3iCJqS+uV6t1ZTjp6enq/fr5+T8AAAD//wMAnbpj1Z4FAAA=
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c40d163fbaf171a-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 16 Sep 2024 12:21:55 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Robots-Tag:
      - none
      anthropic-ratelimit-requests-limit:
      - '4000'
      anthropic-ratelimit-requests-remaining:
      - '3999'
      anthropic-ratelimit-requests-reset:
      - '2024-09-16T12:22:54Z'
      anthropic-ratelimit-tokens-limit:
      - '400000'
      anthropic-ratelimit-tokens-remaining:
      - '385000'
      anthropic-ratelimit-tokens-reset:
      - '2024-09-16T12:21:57Z'
      request-id:
      - req_01MM9PUiPxHppC22Lt817S9z
      via:
      - 1.1 google
      x-cloud-trace-context:
      - ebe5443fb090afe5c061619138c51deb
    status:
      code: 200
      message: OK
- request:
    body: '{"max_tokens": 1024, "messages": [{"role": "user", "content": "You are
      doing web research on behalf of a user. You are trying to figure out this information:\n\n<info>\n{\n  \"type\":
      \"object\",\n  \"properties\": {\n    \"founder\": {\n      \"type\": \"string\",\n      \"description\":
      \"The name of the company founder.\"\n    },\n    \"websiteUrl\": {\n      \"type\":
      \"string\",\n      \"description\": \"Website URL of the company, e.g.: https://openai.com/,
      or https://microsoft.com\"\n    },\n    \"products_sold\": {\n      \"type\":
      \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      },\n      \"description\":
      \"A list of products sold by the company.\"\n    }\n  },\n  \"required\": [\n    \"founder\",\n    \"websiteUrl\",\n    \"products_sold\"\n  ]\n}\n</info>\n\nYou
      have access to the following tools:\n\n- `Search`: call a search tool and get
      back some results\n- `ScrapeWebsite`: scrape a website and get relevant notes
      about the given request. This will update the notes above.\n- `Info`: call this
      when you are done and have gathered all the relevant info\n\nHere is the information
      you have about the topic you are researching:\n\nTopic: LangChain"}, {"role":
      "assistant", "content": [{"text": "Certainly! I''ll use the available tools
      to gather information about LangChain, focusing on the founder, website URL,
      and products sold. Let''s start with a general search to get an overview.",
      "type": "text"}, {"type": "tool_use", "name": "search", "input": {"query": "LangChain
      company founder website products"}, "id": "toolu_01U9aJPZLUKVzQPYxMH4f32A"}]},
      {"role": "user", "content": [{"type": "tool_result", "content": "[{\"url\":
      \"https://www.cbinsights.com/company/langchain\", \"content\": \"It was founded
      in 2023 and is based in San Francisco, California. LangChain specializes in
      the development of large language model (LLM) applications, providing a suite
      of products that support developers throughout the application lifecycle. Use
      the CB Insights Platform to explore LangChain''s full profile.\"}, {\"url\":
      \"https://en.wikipedia.org/wiki/LangChain\", \"content\": \"In October 2023
      LangChain introduced LangServe, a deployment tool designed to facilitate the
      transition from LCEL (LangChain Expression Language) prototypes to production-ready
      applications.[5]\\nIntegrations[edit]\\nAs of March 2023, LangChain included
      integrations with systems including Amazon, Google, and Microsoft Azure cloud
      storage; API wrappers for news, movie information, and weather; Bash for summarization,
      syntax and semantics checking, and execution of shell scripts; multiple web
      scraping subsystems and templates; few-shot learning prompt generation support;
      finding and summarizing \\\"todo\\\" tasks in code; Google Drive documents,
      spreadsheets, and presentations summarization, extraction, and creation; Google
      Search and Microsoft Bing web search; OpenAI, Anthropic, and Hugging Face language
      models; iFixit repair guides and wikis search and summarization; MapReduce for
      question answering, combining documents, and question generation; N-gram overlap
      scoring; PyPDF, pdfminer, fitz, and pymupdf for PDF file text extraction and
      manipulation; Python and JavaScript code generation, analysis, and debugging;
      Milvus vector database[6] to store and retrieve vector embeddings; Weaviate
      vector database[7] to cache embedding and data objects; Redis cache database
      storage; Python RequestsWrapper and other methods for API requests; SQL and
      NoSQL databases including JSON support; Streamlit, including for logging; text
      mapping for k-nearest neighbors search; time zone conversion and calendar operations;
      tracing and recording stack symbols in threaded and asynchronous subprocess
      runs; and the Wolfram Alpha website and SDK.[8] As a language model integration
      framework, LangChain''s use-cases largely overlap with those of language models
      in general, including document analysis and summarization, chatbots, and code
      analysis.[2]\\nHistory[edit]\\nLangChain was launched in October 2022 as an
      open source project by Harrison Chase, while working at machine learning startup
      Robust Intelligence. In April 2023, LangChain had incorporated and the new startup
      raised over $20 million in funding at a valuation of at least $200 million from
      venture firm Sequoia Capital, a week after announcing a $10 million seed investment
      from Benchmark.[3][4]\\n The project quickly garnered popularity, with improvements
      from hundreds of contributors on GitHub, trending discussions on Twitter, lively
      activity on the project''s Discord server, many YouTube tutorials, and meetups
      in San Francisco and London. As of April 2023, it can read from more than 50
      document types and data sources.[9]\\nReferences[edit]\\nExternal links[edit]\"},
      {\"url\": \"https://www.forbes.com/companies/langchain/\", \"content\": \"LangChain.
      Founded in late 2022, LangChain was an instant hit in Silicon Valley for its
      software that enables anyone to build apps powered by large language models
      like GPT-4 in as little as two ...\"}, {\"url\": \"https://tedai-sanfrancisco.ted.com/speakers-1/harrison-chase-/co-founder-and-ceo,-langchain/\",
      \"content\": \"Harrison Chase, a Harvard graduate in statistics and computer
      science, co-founded LangChain to streamline the development of Language Model
      applications with open-source Python/Typescript packages. Chase''s experience
      includes heading the Machine Learning team at Robust Intelligence, focusing
      on the testing and validation of machine learning models, and leading the entity
      linking team at Kensho ...\"}, {\"url\": \"https://www.crunchbase.com/organization/langchain\",
      \"content\": \"Summary. LangChain may be growing as evidenced by securing $25
      million in funding, which is a significant financial milestone for the company.
      Additionally, the launch of a new platform to facilitate the full lifecycle
      of Large Language Model applications suggests expansion in their product offerings.
      The integration of Gemini Pro API, enabling ...\"}, {\"url\": \"https://craft.co/langchain\",
      \"content\": \"LangChain has 5 employees at their 1 location and $35 m in total
      funding,. See insights on LangChain including office locations, competitors,
      revenue, financials, executives, subsidiaries and more at Craft. ... Harrison
      Chase, Co-Founder and Chief Executive Officer. Linkedin;\"}, {\"url\": \"https://www.langchain.com/about\",
      \"content\": \"Ready to start shipping\\nreliable GenAI apps faster?\\nLangChain
      and LangSmith are critical parts of the reference\\narchitecture to get you
      from prototype to production. Announcing the General Availability of LangSmith\\nToday,
      we\\u2019re thrilled to announce the general availability of LangSmith \\u2014
      our solution for LLM application development, monitoring, and testing.\\n We
      build products that enable developers to go from an idea to working code in
      an afternoon and in the hands of users in days or weeks. The biggest developer
      community in \\u00a0GenAI\\nLearn alongside the 100K+ practitioners\\nwho are
      pushing the industry forward.\\n And we built LangSmith to support all stages
      of the AI engineering lifecycle, to get applications into production faster.\\n\"},
      {\"url\": \"https://www.bluelabellabs.com/blog/what-is-langchain/\", \"content\":
      \"Similar setups to AutoTweeter would allow businesses to parse past and present
      customer queries to understand drop-off points and build the appropriate flows
      to solve customer problems on the spot, instead of spitting them into a queue
      to wait for a human and plot how they\\u2019re going to steal from your business.\\n
      Rather than creating a bot to tweet in your tone on your behalf about your love
      for processed snacks, hockey, and political happenings, you could train such
      a system on your brand voice, knowledge bases, and other resources to create
      a highly valuable feature for your digital touchpoints.\\n Here, you essentially
      create a curated ML system that\\u2019s tailored to your brand with specific
      issues and requests from your customers \\u2013 with a little bit of dev magic,
      you can quickly build a solution that integrates features like payment systems,
      tech support, ordering, account management, and so much more.\\n Creating a
      bot for social media that can post just like you\\nSocial media already has
      enough bots engaging with people as if they\\u2019re real people, but for the
      sake of making a functional example, our co-founder Bobby created a bot called
      AutoTweeter for the social media platform we all still call Twitter.\\n How
      does LangChain work?\\nLangchain essentially serves as a middleman between other
      LLMs, data sources, and API-accessible functions \\u2013 more precisely, it\\u2019s
      a comprehensive open-source library of components that allow users to build
      better (i.e., more custom) solutions by interconnecting different resources.\\n\"},
      {\"url\": \"https://www.latent.space/p/langchain\", \"content\": \"And so you
      can do a lot of things like reflection and kind of like making sure that you''re
      on the right track and really put more safeguards and thinking about these agents
      as opposed to relying on like chas and interface, like the bot we have that''s
      on GitHub answering questions on the issues, I think probably gives better answers
      than the bots that we have that are on chat on the website. [00:50:32]\\nHarrison:
      And then between kind of like allowing for, I think one helpful thing that we
      did in terms of like distinguishing between basically the base interfaces and
      then more complex stuff is part of the separation around the docs is there''s
      like the components piece, which has the model IO, the retrieval, the agents,
      the callbacks, things like that. And then like, I know a lot of people are doing
      this, but like a personal assistant for like managing like email calendar, basic
      stuff, which I think is, I think that''s like a fantastic application for these
      like agent like things, because if you think about personal assistants today,
      you usually interact, I don''t have one, but I''m told you interact with them
      over email. Show Notes\\nLangChain\\nLangChain\\u2019s Berkshire Hathaway Homepage\\nAbstractions
      tweet\\nLangSmith\\nLangSmith Cookbooks repo\\nLangChain Retrieval blog\\nEvaluating
      CSV Question/Answering blog and YouTube\\nMultiOn Partner blog\\nHarvard Sports
      Analytics Collective\\nEvaluating RAG Webinar\\nawesome-langchain:\\nLLM Math
      Chain\\nSelf-Ask\\nLangChain Hub UI\\n\\u201cLangChain is Pointless\\u201d\\nHarrison\\u2019s
      links\\nsports - estimating player compatibility in the NBA\\nearly interest
      in prompt injections\\nGitHub\\nTwitter\\nTimestamps\\n[00:00:00] Introduction\\n[00:00:48]
      Harrison''s background and how sports led him into ML\\n[00:04:54] Memory (for
      long running chats, whether a simple Buffer, Knowledge Graph, Summary, or Vector
      Store)\\nUse-Cases: compositions of Components\\nChains: combining a PromptTemplate,
      LLM Model and optional OutputParser\\nwith Router, Sequential, and Transform
      Chains for advanced usecases\\nsavable, sharable schemas that can be loaded
      from LangChainHub\\nAgents: a chain that has access to a suite of tools, of
      nondeterministic8 length because the LLM is used as a reasoning engine to determine
      which actions to take and in which order.\"}, {\"url\": \"https://www.langchain.com/\",
      \"content\": \"Join Discord\\nContribute on GitHub\\n5.3M\\nMonthly Downloads\\n67k\\nGithub
      Stars\\n1800+\\nContributors\\n34k+\\nApps Powered\\nReady to build?\\nGo To
      Docs\\nGo To Docs\\nGo To Docs\\nContact Sales\\nContact Sales\\nContact Sales\\nProducts
      and use-cases\\nLangChain\\nLangSmith\\nRetrieval\\nAgents\\nInspiration\\nCode\\nGitHub\\nLangChain
      Hub\\nPython Docs\\nJS/TS Docs\\nSocial\\nTwitter\\nDiscord\\nBlog\\nLinkedIn\\nYouTube\\nTerms
      of Service\\nSign up for our newsletter\\nProducts and use-cases\\nLangChain\\nLangSmith\\nRetrieval\\nAgents\\nInspiration\\nCode\\nGitHub\\nLangChain
      Hub\\nPython Docs\\nJS/TS Docs\\nSocial\\nTwitter\\nDiscord\\nBlog\\nLinkedIn\\nYouTube\\nTerms
      of Service\\nSign up for our newsletter\\nProducts and use-cases\\nLangChain\\nLangSmith\\nRetrieval\\nAgents\\nInspiration\\nCode\\nGitHub\\nLangChain
      Hub\\nPython Docs\\nJS/TS Docs\\nSocial\\nTwitter\\nDiscord\\nBlog\\nLinkedIn\\nYouTube\\nTerms
      of Service\\nSign up for our newsletter Chatbots\\nQ&A Over Docs\\nSummarization\\nCopilots\\nWorkflow
      Automation\\nDocument Analysis\\nCustom Search\\nHarness the power\\u2013and
      wrangle the complexity\\u2013 of LLMs\\nGet from proof-of-concept to production
      on one platform\\nRely on a set of tools built especially for non-deterministic
      models\\nLeverage new cognitive architectures and battle-tested orchestration\\nThanks
      to our contributors and partners for making LangChain awesome\\nWhen you build
      with LangChain, you build alongside a community of developers advancing the
      frontier of what\\u2019s possible with LLMs.\\n \\ud83e\\udd9c\\ud83d\\udd17
      LangChain\\nLangSmith\\nLangServe\\nAgents\\nRetrieval\\nEvaluation\\nBlog\\nDocs\\n\\ud83e\\udd9c\\ud83d\\udd17
      LangChain\\n\\ud83e\\udd9c\\ud83d\\udd17 LangChain\\nLangChain Expression Language
      makes it easy to create custom chains.\\n Go To Docs\\nGo To Docs\\nGo To Docs\\nContact
      Sales\\nglobal corporations, STARTUPS, and TINKERERS build with LangChain\\nglobal
      corporations, STARTUPS, and TINKERERS build with LangChain\\nglobal corporations,
      STARTUPS, and TINKERERS build with LangChain\\nA complete set of powerful building
      blocks\\nA complete set of powerful building blocks\\n Try it out here\\nGet
      your LLM application from prototype to production\\nGet your LLM application
      from prototype to production\\nBuild context-aware, reasoning applications with
      LangChain\\u2019s flexible abstractions and AI-first toolkit.\\n\"}]", "tool_use_id":
      "toolu_01U9aJPZLUKVzQPYxMH4f32A", "is_error": false}]}, {"role": "assistant",
      "content": [{"text": "Based on the search results, I''ve gathered some information
      about LangChain. Let''s summarize the key points:\n\n1. Founder: Harrison Chase\n2.
      Website URL: https://www.langchain.com/\n3. Products sold: LangChain appears
      to offer several products and services related to large language model (LLM)
      applications. Let''s gather more specific information about their products by
      scraping their website.\n\nNow, let''s scrape the LangChain website to get more
      detailed information about their products:", "type": "text"}, {"type": "tool_use",
      "name": "scrape_website", "input": {"url": "https://www.langchain.com/"}, "id":
      "toolu_019NAePCSLsWtqwTK7yRPhwq"}]}, {"role": "user", "content": [{"type": "tool_result",
      "content": "Here are some key notes about the LangChain website based on the
      content:\n\n1. LangChain offers a suite of products for building, running, and
      managing LLM applications:\n   - LangChain: A framework for building with LLMs\n   -
      LangGraph: A framework for building controllable agentic workflows\n   - LangSmith:
      A platform for debugging, testing, deploying and monitoring LLM apps\n\n2. The
      website highlights that LangChain products can be used independently or together.\n\n3.
      Key stats mentioned:\n   - 15M+ monthly downloads\n   - 100K+ apps powered\n   -
      75K+ GitHub stars\n   - 3K+ contributors\n\n4. The site emphasizes LangChain''s
      large developer community (1M+ developers).\n\n5. There are testimonials from
      companies like Retool, Rakuten, and Elastic about using LangChain products.\n\n6.
      The website offers resources like documentation, GitHub links, blog, and community
      pages.\n\n7. There''s a newsletter signup form in the footer.\n\n8. The site
      promotes both free signup and enterprise demos.\n\n9. Use cases across industries
      like financial services, fintech, and technology are highlighted.\n\n10. The
      website design uses a lot of green coloring, matching LangChain''s branding.",
      "tool_use_id": "toolu_019NAePCSLsWtqwTK7yRPhwq", "is_error": false}]}], "model":
      "claude-3-5-sonnet-20240620", "tools": [{"name": "scrape_website", "description":
      "Scrape and summarize content from a given URL.", "input_schema": {"type": "object",
      "properties": {"url": {"type": "string"}}, "required": ["url"]}}, {"name": "search",
      "description": "Search for general results. This function performs a search
      using the Tavily search engine, which is designed\nto provide comprehensive,
      accurate, and trusted results. It''s particularly useful\nfor answering questions
      about current events.", "input_schema": {"type": "object", "properties": {"query":
      {"type": "string"}}, "required": ["query"]}}, {"name": "Info", "description":
      "Call this when you have gathered all the relevant info", "input_schema": {"type":
      "object", "properties": {"founder": {"type": "string", "description": "The name
      of the company founder."}, "websiteUrl": {"type": "string", "description": "Website
      URL of the company, e.g.: https://openai.com/, or https://microsoft.com"}, "products_sold":
      {"type": "array", "items": {"type": "string"}, "description": "A list of products
      sold by the company."}}, "required": ["founder", "websiteUrl", "products_sold"]}}]}'
    headers: {}
    method: POST
    uri: https://api.anthropic.com/v1/messages
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA1SS3WobMRCFX0XMTW/kn3VtF3RZQ5NCMAUnTUoIRt2dXQlrZzbSyFvH+N3LbtI2
        vdJoZr6DzkFn8BUYaFOznxe398Vx+yXV3/2Ph9Q+LL9t7l6qBBrk1OGwhSnZBkFD5DA0bEo+iSUB
        DS1XGMBAGWyucPJxspokJkKZLOaL5Xy9mIOGkkmQBMzj+Y+o4K8BHw8Dt87SQZ04q5qj6iIffeWp
        UeJQpTLaDivlqebYWvFMqo7cjsMbS83GWU+qx5/JC07VlnslzorqUTl7RNVYcRixUjaEESIsB0vx
        9F5Tq4DyIamS284HVF6UJ+ERiPic/aDwum3gov8ZYQ77nIZ4xkyHe97Pi5vtwTX31+3V5/V29+ko
        L4XDXQ8ayLYD95VqHhjqsoA5Q82ZKoxg4NrG6BOT2jg76r5Zu4tD0E6kS2Y26/t+Giw15eB+WnI7
        Aw1d5CqXkvaJQwXmEf7mA3qsr6Lt3Fu9a704eLpcnjQk4W4f0Sam/z2Ng4TPGalEMJRD0JDH/2DO
        r6/fCx+QEpjlelVo4Czvm8WquFx+AwAA//8DACBWGIVwAgAA
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c40d1affa7ffb30-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 16 Sep 2024 12:21:58 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Robots-Tag:
      - none
      anthropic-ratelimit-requests-limit:
      - '4000'
      anthropic-ratelimit-requests-remaining:
      - '3999'
      anthropic-ratelimit-requests-reset:
      - '2024-09-16T12:22:54Z'
      anthropic-ratelimit-tokens-limit:
      - '400000'
      anthropic-ratelimit-tokens-remaining:
      - '400000'
      anthropic-ratelimit-tokens-reset:
      - '2024-09-16T12:21:58Z'
      request-id:
      - req_019H8WzPxAotL895qqCZ16sq
      via:
      - 1.1 google
      x-cloud-trace-context:
      - 122ad422965482bec99aadd8fb55bddd
    status:
      code: 200
      message: OK
- request:
    body: '{"max_tokens": 1024, "messages": [{"role": "user", "content": "You are
      doing web research on behalf of a user. You are trying to figure out this information:\n\n<info>\n{\n  \"type\":
      \"object\",\n  \"properties\": {\n    \"founder\": {\n      \"type\": \"string\",\n      \"description\":
      \"The name of the company founder.\"\n    },\n    \"websiteUrl\": {\n      \"type\":
      \"string\",\n      \"description\": \"Website URL of the company, e.g.: https://openai.com/,
      or https://microsoft.com\"\n    },\n    \"products_sold\": {\n      \"type\":
      \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      },\n      \"description\":
      \"A list of products sold by the company.\"\n    }\n  },\n  \"required\": [\n    \"founder\",\n    \"websiteUrl\",\n    \"products_sold\"\n  ]\n}\n</info>\n\nYou
      have access to the following tools:\n\n- `Search`: call a search tool and get
      back some results\n- `ScrapeWebsite`: scrape a website and get relevant notes
      about the given request. This will update the notes above.\n- `Info`: call this
      when you are done and have gathered all the relevant info\n\nHere is the information
      you have about the topic you are researching:\n\nTopic: LangChain"}, {"role":
      "assistant", "content": [{"text": "Certainly! I''ll use the available tools
      to gather information about LangChain, focusing on the founder, website URL,
      and products sold. Let''s start with a general search to get an overview.",
      "type": "text"}, {"type": "tool_use", "name": "search", "input": {"query": "LangChain
      company founder website products"}, "id": "toolu_01U9aJPZLUKVzQPYxMH4f32A"}]},
      {"role": "user", "content": [{"type": "tool_result", "content": "[{\"url\":
      \"https://www.cbinsights.com/company/langchain\", \"content\": \"It was founded
      in 2023 and is based in San Francisco, California. LangChain specializes in
      the development of large language model (LLM) applications, providing a suite
      of products that support developers throughout the application lifecycle. Use
      the CB Insights Platform to explore LangChain''s full profile.\"}, {\"url\":
      \"https://en.wikipedia.org/wiki/LangChain\", \"content\": \"In October 2023
      LangChain introduced LangServe, a deployment tool designed to facilitate the
      transition from LCEL (LangChain Expression Language) prototypes to production-ready
      applications.[5]\\nIntegrations[edit]\\nAs of March 2023, LangChain included
      integrations with systems including Amazon, Google, and Microsoft Azure cloud
      storage; API wrappers for news, movie information, and weather; Bash for summarization,
      syntax and semantics checking, and execution of shell scripts; multiple web
      scraping subsystems and templates; few-shot learning prompt generation support;
      finding and summarizing \\\"todo\\\" tasks in code; Google Drive documents,
      spreadsheets, and presentations summarization, extraction, and creation; Google
      Search and Microsoft Bing web search; OpenAI, Anthropic, and Hugging Face language
      models; iFixit repair guides and wikis search and summarization; MapReduce for
      question answering, combining documents, and question generation; N-gram overlap
      scoring; PyPDF, pdfminer, fitz, and pymupdf for PDF file text extraction and
      manipulation; Python and JavaScript code generation, analysis, and debugging;
      Milvus vector database[6] to store and retrieve vector embeddings; Weaviate
      vector database[7] to cache embedding and data objects; Redis cache database
      storage; Python RequestsWrapper and other methods for API requests; SQL and
      NoSQL databases including JSON support; Streamlit, including for logging; text
      mapping for k-nearest neighbors search; time zone conversion and calendar operations;
      tracing and recording stack symbols in threaded and asynchronous subprocess
      runs; and the Wolfram Alpha website and SDK.[8] As a language model integration
      framework, LangChain''s use-cases largely overlap with those of language models
      in general, including document analysis and summarization, chatbots, and code
      analysis.[2]\\nHistory[edit]\\nLangChain was launched in October 2022 as an
      open source project by Harrison Chase, while working at machine learning startup
      Robust Intelligence. In April 2023, LangChain had incorporated and the new startup
      raised over $20 million in funding at a valuation of at least $200 million from
      venture firm Sequoia Capital, a week after announcing a $10 million seed investment
      from Benchmark.[3][4]\\n The project quickly garnered popularity, with improvements
      from hundreds of contributors on GitHub, trending discussions on Twitter, lively
      activity on the project''s Discord server, many YouTube tutorials, and meetups
      in San Francisco and London. As of April 2023, it can read from more than 50
      document types and data sources.[9]\\nReferences[edit]\\nExternal links[edit]\"},
      {\"url\": \"https://www.forbes.com/companies/langchain/\", \"content\": \"LangChain.
      Founded in late 2022, LangChain was an instant hit in Silicon Valley for its
      software that enables anyone to build apps powered by large language models
      like GPT-4 in as little as two ...\"}, {\"url\": \"https://tedai-sanfrancisco.ted.com/speakers-1/harrison-chase-/co-founder-and-ceo,-langchain/\",
      \"content\": \"Harrison Chase, a Harvard graduate in statistics and computer
      science, co-founded LangChain to streamline the development of Language Model
      applications with open-source Python/Typescript packages. Chase''s experience
      includes heading the Machine Learning team at Robust Intelligence, focusing
      on the testing and validation of machine learning models, and leading the entity
      linking team at Kensho ...\"}, {\"url\": \"https://www.crunchbase.com/organization/langchain\",
      \"content\": \"Summary. LangChain may be growing as evidenced by securing $25
      million in funding, which is a significant financial milestone for the company.
      Additionally, the launch of a new platform to facilitate the full lifecycle
      of Large Language Model applications suggests expansion in their product offerings.
      The integration of Gemini Pro API, enabling ...\"}, {\"url\": \"https://craft.co/langchain\",
      \"content\": \"LangChain has 5 employees at their 1 location and $35 m in total
      funding,. See insights on LangChain including office locations, competitors,
      revenue, financials, executives, subsidiaries and more at Craft. ... Harrison
      Chase, Co-Founder and Chief Executive Officer. Linkedin;\"}, {\"url\": \"https://www.langchain.com/about\",
      \"content\": \"Ready to start shipping\\nreliable GenAI apps faster?\\nLangChain
      and LangSmith are critical parts of the reference\\narchitecture to get you
      from prototype to production. Announcing the General Availability of LangSmith\\nToday,
      we\\u2019re thrilled to announce the general availability of LangSmith \\u2014
      our solution for LLM application development, monitoring, and testing.\\n We
      build products that enable developers to go from an idea to working code in
      an afternoon and in the hands of users in days or weeks. The biggest developer
      community in \\u00a0GenAI\\nLearn alongside the 100K+ practitioners\\nwho are
      pushing the industry forward.\\n And we built LangSmith to support all stages
      of the AI engineering lifecycle, to get applications into production faster.\\n\"},
      {\"url\": \"https://www.bluelabellabs.com/blog/what-is-langchain/\", \"content\":
      \"Similar setups to AutoTweeter would allow businesses to parse past and present
      customer queries to understand drop-off points and build the appropriate flows
      to solve customer problems on the spot, instead of spitting them into a queue
      to wait for a human and plot how they\\u2019re going to steal from your business.\\n
      Rather than creating a bot to tweet in your tone on your behalf about your love
      for processed snacks, hockey, and political happenings, you could train such
      a system on your brand voice, knowledge bases, and other resources to create
      a highly valuable feature for your digital touchpoints.\\n Here, you essentially
      create a curated ML system that\\u2019s tailored to your brand with specific
      issues and requests from your customers \\u2013 with a little bit of dev magic,
      you can quickly build a solution that integrates features like payment systems,
      tech support, ordering, account management, and so much more.\\n Creating a
      bot for social media that can post just like you\\nSocial media already has
      enough bots engaging with people as if they\\u2019re real people, but for the
      sake of making a functional example, our co-founder Bobby created a bot called
      AutoTweeter for the social media platform we all still call Twitter.\\n How
      does LangChain work?\\nLangchain essentially serves as a middleman between other
      LLMs, data sources, and API-accessible functions \\u2013 more precisely, it\\u2019s
      a comprehensive open-source library of components that allow users to build
      better (i.e., more custom) solutions by interconnecting different resources.\\n\"},
      {\"url\": \"https://www.latent.space/p/langchain\", \"content\": \"And so you
      can do a lot of things like reflection and kind of like making sure that you''re
      on the right track and really put more safeguards and thinking about these agents
      as opposed to relying on like chas and interface, like the bot we have that''s
      on GitHub answering questions on the issues, I think probably gives better answers
      than the bots that we have that are on chat on the website. [00:50:32]\\nHarrison:
      And then between kind of like allowing for, I think one helpful thing that we
      did in terms of like distinguishing between basically the base interfaces and
      then more complex stuff is part of the separation around the docs is there''s
      like the components piece, which has the model IO, the retrieval, the agents,
      the callbacks, things like that. And then like, I know a lot of people are doing
      this, but like a personal assistant for like managing like email calendar, basic
      stuff, which I think is, I think that''s like a fantastic application for these
      like agent like things, because if you think about personal assistants today,
      you usually interact, I don''t have one, but I''m told you interact with them
      over email. Show Notes\\nLangChain\\nLangChain\\u2019s Berkshire Hathaway Homepage\\nAbstractions
      tweet\\nLangSmith\\nLangSmith Cookbooks repo\\nLangChain Retrieval blog\\nEvaluating
      CSV Question/Answering blog and YouTube\\nMultiOn Partner blog\\nHarvard Sports
      Analytics Collective\\nEvaluating RAG Webinar\\nawesome-langchain:\\nLLM Math
      Chain\\nSelf-Ask\\nLangChain Hub UI\\n\\u201cLangChain is Pointless\\u201d\\nHarrison\\u2019s
      links\\nsports - estimating player compatibility in the NBA\\nearly interest
      in prompt injections\\nGitHub\\nTwitter\\nTimestamps\\n[00:00:00] Introduction\\n[00:00:48]
      Harrison''s background and how sports led him into ML\\n[00:04:54] Memory (for
      long running chats, whether a simple Buffer, Knowledge Graph, Summary, or Vector
      Store)\\nUse-Cases: compositions of Components\\nChains: combining a PromptTemplate,
      LLM Model and optional OutputParser\\nwith Router, Sequential, and Transform
      Chains for advanced usecases\\nsavable, sharable schemas that can be loaded
      from LangChainHub\\nAgents: a chain that has access to a suite of tools, of
      nondeterministic8 length because the LLM is used as a reasoning engine to determine
      which actions to take and in which order.\"}, {\"url\": \"https://www.langchain.com/\",
      \"content\": \"Join Discord\\nContribute on GitHub\\n5.3M\\nMonthly Downloads\\n67k\\nGithub
      Stars\\n1800+\\nContributors\\n34k+\\nApps Powered\\nReady to build?\\nGo To
      Docs\\nGo To Docs\\nGo To Docs\\nContact Sales\\nContact Sales\\nContact Sales\\nProducts
      and use-cases\\nLangChain\\nLangSmith\\nRetrieval\\nAgents\\nInspiration\\nCode\\nGitHub\\nLangChain
      Hub\\nPython Docs\\nJS/TS Docs\\nSocial\\nTwitter\\nDiscord\\nBlog\\nLinkedIn\\nYouTube\\nTerms
      of Service\\nSign up for our newsletter\\nProducts and use-cases\\nLangChain\\nLangSmith\\nRetrieval\\nAgents\\nInspiration\\nCode\\nGitHub\\nLangChain
      Hub\\nPython Docs\\nJS/TS Docs\\nSocial\\nTwitter\\nDiscord\\nBlog\\nLinkedIn\\nYouTube\\nTerms
      of Service\\nSign up for our newsletter\\nProducts and use-cases\\nLangChain\\nLangSmith\\nRetrieval\\nAgents\\nInspiration\\nCode\\nGitHub\\nLangChain
      Hub\\nPython Docs\\nJS/TS Docs\\nSocial\\nTwitter\\nDiscord\\nBlog\\nLinkedIn\\nYouTube\\nTerms
      of Service\\nSign up for our newsletter Chatbots\\nQ&A Over Docs\\nSummarization\\nCopilots\\nWorkflow
      Automation\\nDocument Analysis\\nCustom Search\\nHarness the power\\u2013and
      wrangle the complexity\\u2013 of LLMs\\nGet from proof-of-concept to production
      on one platform\\nRely on a set of tools built especially for non-deterministic
      models\\nLeverage new cognitive architectures and battle-tested orchestration\\nThanks
      to our contributors and partners for making LangChain awesome\\nWhen you build
      with LangChain, you build alongside a community of developers advancing the
      frontier of what\\u2019s possible with LLMs.\\n \\ud83e\\udd9c\\ud83d\\udd17
      LangChain\\nLangSmith\\nLangServe\\nAgents\\nRetrieval\\nEvaluation\\nBlog\\nDocs\\n\\ud83e\\udd9c\\ud83d\\udd17
      LangChain\\n\\ud83e\\udd9c\\ud83d\\udd17 LangChain\\nLangChain Expression Language
      makes it easy to create custom chains.\\n Go To Docs\\nGo To Docs\\nGo To Docs\\nContact
      Sales\\nglobal corporations, STARTUPS, and TINKERERS build with LangChain\\nglobal
      corporations, STARTUPS, and TINKERERS build with LangChain\\nglobal corporations,
      STARTUPS, and TINKERERS build with LangChain\\nA complete set of powerful building
      blocks\\nA complete set of powerful building blocks\\n Try it out here\\nGet
      your LLM application from prototype to production\\nGet your LLM application
      from prototype to production\\nBuild context-aware, reasoning applications with
      LangChain\\u2019s flexible abstractions and AI-first toolkit.\\n\"}]", "tool_use_id":
      "toolu_01U9aJPZLUKVzQPYxMH4f32A", "is_error": false}]}, {"role": "assistant",
      "content": [{"text": "Based on the search results, I''ve gathered some information
      about LangChain. Let''s summarize the key points:\n\n1. Founder: Harrison Chase\n2.
      Website URL: https://www.langchain.com/\n3. Products sold: LangChain appears
      to offer several products and services related to large language model (LLM)
      applications. Let''s gather more specific information about their products by
      scraping their website.\n\nNow, let''s scrape the LangChain website to get more
      detailed information about their products:", "type": "text"}, {"type": "tool_use",
      "name": "scrape_website", "input": {"url": "https://www.langchain.com/"}, "id":
      "toolu_019NAePCSLsWtqwTK7yRPhwq"}]}, {"role": "user", "content": [{"type": "tool_result",
      "content": "Here are some key notes about the LangChain website based on the
      content:\n\n1. LangChain offers a suite of products for building, running, and
      managing LLM applications:\n   - LangChain: A framework for building with LLMs\n   -
      LangGraph: A framework for building controllable agentic workflows\n   - LangSmith:
      A platform for debugging, testing, deploying and monitoring LLM apps\n\n2. The
      website highlights that LangChain products can be used independently or together.\n\n3.
      Key stats mentioned:\n   - 15M+ monthly downloads\n   - 100K+ apps powered\n   -
      75K+ GitHub stars\n   - 3K+ contributors\n\n4. The site emphasizes LangChain''s
      large developer community (1M+ developers).\n\n5. There are testimonials from
      companies like Retool, Rakuten, and Elastic about using LangChain products.\n\n6.
      The website offers resources like documentation, GitHub links, blog, and community
      pages.\n\n7. There''s a newsletter signup form in the footer.\n\n8. The site
      promotes both free signup and enterprise demos.\n\n9. Use cases across industries
      like financial services, fintech, and technology are highlighted.\n\n10. The
      website design uses a lot of green coloring, matching LangChain''s branding.",
      "tool_use_id": "toolu_019NAePCSLsWtqwTK7yRPhwq", "is_error": false}, {"type":
      "text", "text": "I am thinking of calling the info tool with the info below.
      Is this good? Give your reasoning as well. You can encourage the Assistant to
      look at specific URLs if that seems relevant, or do more searches.\nIf you don''t
      think it is good, you should be very specific about what could be improved.\n\n{\n  \"founder\":
      \"Harrison Chase\",\n  \"websiteUrl\": \"https://www.langchain.com/\",\n  \"products_sold\":
      [\n    \"LangChain\",\n    \"LangGraph\",\n    \"LangSmith\"\n  ]\n}"}]}], "model":
      "claude-3-5-sonnet-20240620", "tool_choice": {"type": "tool", "name": "InfoIsSatisfactory"},
      "tools": [{"name": "InfoIsSatisfactory", "description": "Validate whether the
      current extracted info is satisfactory and complete.", "input_schema": {"properties":
      {"reason": {"description": "First, provide reasoning for why this is either
      good or bad as a final result. Must include at least 3 reasons.", "items": {"type":
      "string"}, "type": "array"}, "is_satisfactory": {"description": "After providing
      your reasoning, provide a value indicating whether the result is satisfactory.
      If not, you will continue researching.", "type": "boolean"}}, "required": ["reason",
      "is_satisfactory"], "type": "object"}}]}'
    headers: {}
    method: POST
    uri: https://api.anthropic.com/v1/messages
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA3yTUW8TMQzHv4qVF0BKp67rCtwbG7Bu2guMDU3TVKWJrxeWc26xQ1WqfneUawtl
        DzzGiX/++29nrbxTlWp5MRsevxvx2dP96XR8dX9xkybdp+7DRfyutJJVh+UVMpsFKq1SDCVgmD2L
        IVFatdFhUJWywWSHg5PB6YAjEcpgNByNh5PRUGllIwmSqOphvYdKjGGWuVB7KeWcZ8PjL/i2vaOr
        8ag7ydP38uPu7OPnX15pRaYteZdUx0u+MeK5NlZiWhUCdVlUtVYJDUdS1YP61iB4qmNqjfhIsDDS
        YEIHNv7ExGBCgITP2ZdY7TE4htd1zOQwaVjinL3gbQoaDDnoUnTZCs84BvcGDAN3aH3t0YEnkL6Y
        F28CsG2wNUdK9xJ2xFcMRb+GqUnJcyQ4bwyjBs9gY0poJazAOyTZQueG0UEkaHMQ3wUEjjlZZKhT
        bPuCjCbZBhJyDsL7gjvlcPv1usCNtTkZwb6L1ohtkPvsWNfeFr3XhhbnjfG0T92T/ukZgmcBTzZk
        tyNIkxChLZn7p4Xamzxf/eXqYpeNVPvUbq/kQCfbZDpPi5d9HM6uN4ms520bzu/8MsTLMsvCeznL
        pZcmZoFMhLYscFqBQzE+/CnhjJjC3jrrts4mDN7Mw7ZS7gYSB674d6BH73woqv9vZQ9J2GXpkYTL
        fTU+Uo9aeZ7x4SZXkjJuNo9ascRutt/mw8/SXzA+ZySLqqIcgla5/5/VevsRZhKfkFhV48noWKuY
        5TA4Gk42m98AAAD//wMAIWYXCAAEAAA=
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c40d1c21f33cf12-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 16 Sep 2024 12:22:03 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Robots-Tag:
      - none
      anthropic-ratelimit-requests-limit:
      - '4000'
      anthropic-ratelimit-requests-remaining:
      - '3999'
      anthropic-ratelimit-requests-reset:
      - '2024-09-16T12:22:54Z'
      anthropic-ratelimit-tokens-limit:
      - '400000'
      anthropic-ratelimit-tokens-remaining:
      - '400000'
      anthropic-ratelimit-tokens-reset:
      - '2024-09-16T12:22:03Z'
      request-id:
      - req_01S5wADpuseuRFbBFQkRq3ny
      via:
      - 1.1 google
      x-cloud-trace-context:
      - 27ece8a162b05ad58192e306f891d2c1
    status:
      code: 200
      message: OK
- request:
    body: '{"max_tokens": 1024, "messages": [{"role": "user", "content": "You are
      doing web research on behalf of a user. You are trying to figure out this information:\n\n<info>\n{\n  \"type\":
      \"object\",\n  \"properties\": {\n    \"founder\": {\n      \"type\": \"string\",\n      \"description\":
      \"The name of the company founder.\"\n    },\n    \"websiteUrl\": {\n      \"type\":
      \"string\",\n      \"description\": \"Website URL of the company, e.g.: https://openai.com/,
      or https://microsoft.com\"\n    },\n    \"products_sold\": {\n      \"type\":
      \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      },\n      \"description\":
      \"A list of products sold by the company.\"\n    }\n  },\n  \"required\": [\n    \"founder\",\n    \"websiteUrl\",\n    \"products_sold\"\n  ]\n}\n</info>\n\nYou
      have access to the following tools:\n\n- `Search`: call a search tool and get
      back some results\n- `ScrapeWebsite`: scrape a website and get relevant notes
      about the given request. This will update the notes above.\n- `Info`: call this
      when you are done and have gathered all the relevant info\n\nHere is the information
      you have about the topic you are researching:\n\nTopic: LangChain"}], "model":
      "claude-3-5-sonnet-20240620", "tool_choice": {"type": "any"}, "tools": [{"name":
      "scrape_website", "description": "Scrape and summarize content from a given
      URL.", "input_schema": {"type": "object", "properties": {"url": {"type": "string"}},
      "required": ["url"]}}, {"name": "search", "description": "Search for general
      results. This function performs a search using the Tavily search engine, which
      is designed\nto provide comprehensive, accurate, and trusted results. It''s
      particularly useful\nfor answering questions about current events.", "input_schema":
      {"type": "object", "properties": {"query": {"type": "string"}}, "required":
      ["query"]}}, {"name": "Info", "description": "Call this when you have gathered
      all the relevant info", "input_schema": {"type": "object", "properties": {"founder":
      {"type": "string", "description": "The name of the company founder."}, "websiteUrl":
      {"type": "string", "description": "Website URL of the company, e.g.: https://openai.com/,
      or https://microsoft.com"}, "products_sold": {"type": "array", "items": {"type":
      "string"}, "description": "A list of products sold by the company."}}, "required":
      ["founder", "websiteUrl", "products_sold"]}}]}'
    headers: {}
    method: POST
    uri: https://api.anthropic.com/v1/messages
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA1RQTUvDQBD9K2HOG0hrrWVv0oNQLLSI0ioS1t1pEk1mkswsUkP+u6Qi6HHevC/e
        AFUAC40UeTbbvh/8njd3wbuy2D1t97vuuVUwoOcWJxaKuALBQM/1BDiRStTRxGk4YA0WfO1iwPQq
        vU6FiVDTeTZfZMt5BgY8kyIp2Jfh11SZ6zzK5HqpMt0xz2YbpYfb074KpbvZ8HF96B6PXx4MkGsm
        naDrfTmpqI0KdoAuYn8GC/eOinXpKko8N62jc3LiSAF7k3zim1SKJnEUkrbnEL0KjOOrAVFu8x6d
        MP1vdXkIdhHJI1iKdW0gXoaww096rvyBJGBXq6UBjvoXWyzH8RsAAP//AwC9EaJ+ZwEAAA==
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c4af82538cb230a-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 17 Sep 2024 17:55:50 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Robots-Tag:
      - none
      anthropic-ratelimit-requests-limit:
      - '4000'
      anthropic-ratelimit-requests-remaining:
      - '3999'
      anthropic-ratelimit-requests-reset:
      - '2024-09-17T17:55:54Z'
      anthropic-ratelimit-tokens-limit:
      - '400000'
      anthropic-ratelimit-tokens-remaining:
      - '400000'
      anthropic-ratelimit-tokens-reset:
      - '2024-09-17T17:55:50Z'
      request-id:
      - req_01FAWNKeAwKQRx8VwSLtkKFB
      via:
      - 1.1 google
      x-cloud-trace-context:
      - 469797dbc792f48da3e4b25f9fe3a121
    status:
      code: 200
      message: OK
- request:
    body: '{"max_tokens": 1024, "messages": [{"role": "user", "content": "You are
      doing web research on behalf of a user. You are trying to figure out this information:\n\n<info>\n{\n  \"type\":
      \"object\",\n  \"properties\": {\n    \"founder\": {\n      \"type\": \"string\",\n      \"description\":
      \"The name of the company founder.\"\n    },\n    \"websiteUrl\": {\n      \"type\":
      \"string\",\n      \"description\": \"Website URL of the company, e.g.: https://openai.com/,
      or https://microsoft.com\"\n    },\n    \"products_sold\": {\n      \"type\":
      \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      },\n      \"description\":
      \"A list of products sold by the company.\"\n    }\n  },\n  \"required\": [\n    \"founder\",\n    \"websiteUrl\",\n    \"products_sold\"\n  ]\n}\n</info>\n\nYou
      have access to the following tools:\n\n- `Search`: call a search tool and get
      back some results\n- `ScrapeWebsite`: scrape a website and get relevant notes
      about the given request. This will update the notes above.\n- `Info`: call this
      when you are done and have gathered all the relevant info\n\nHere is the information
      you have about the topic you are researching:\n\nTopic: LangChain"}, {"role":
      "assistant", "content": [{"type": "tool_use", "name": "search", "input": {"query":
      "LangChain company founder, website, and products"}, "id": "toolu_01JtnSAfQidha7JoYCXqUYzc"}]},
      {"role": "user", "content": [{"type": "tool_result", "content": "[{\"url\":
      \"https://www.cbinsights.com/company/langchain\", \"content\": \"It was founded
      in 2023 and is based in San Francisco, California. LangChain specializes in
      the development of large language model (LLM) applications, providing a suite
      of products that support developers throughout the application lifecycle. Use
      the CB Insights Platform to explore LangChain''s full profile.\"}, {\"url\":
      \"https://en.wikipedia.org/wiki/LangChain\", \"content\": \"In October 2023
      LangChain introduced LangServe, a deployment tool designed to facilitate the
      transition from LCEL (LangChain Expression Language) prototypes to production-ready
      applications.[5]\\nIntegrations[edit]\\nAs of March 2023, LangChain included
      integrations with systems including Amazon, Google, and Microsoft Azure cloud
      storage; API wrappers for news, movie information, and weather; Bash for summarization,
      syntax and semantics checking, and execution of shell scripts; multiple web
      scraping subsystems and templates; few-shot learning prompt generation support;
      finding and summarizing \\\"todo\\\" tasks in code; Google Drive documents,
      spreadsheets, and presentations summarization, extraction, and creation; Google
      Search and Microsoft Bing web search; OpenAI, Anthropic, and Hugging Face language
      models; iFixit repair guides and wikis search and summarization; MapReduce for
      question answering, combining documents, and question generation; N-gram overlap
      scoring; PyPDF, pdfminer, fitz, and pymupdf for PDF file text extraction and
      manipulation; Python and JavaScript code generation, analysis, and debugging;
      Milvus vector database[6] to store and retrieve vector embeddings; Weaviate
      vector database[7] to cache embedding and data objects; Redis cache database
      storage; Python RequestsWrapper and other methods for API requests; SQL and
      NoSQL databases including JSON support; Streamlit, including for logging; text
      mapping for k-nearest neighbors search; time zone conversion and calendar operations;
      tracing and recording stack symbols in threaded and asynchronous subprocess
      runs; and the Wolfram Alpha website and SDK.[8] As a language model integration
      framework, LangChain''s use-cases largely overlap with those of language models
      in general, including document analysis and summarization, chatbots, and code
      analysis.[2]\\nHistory[edit]\\nLangChain was launched in October 2022 as an
      open source project by Harrison Chase, while working at machine learning startup
      Robust Intelligence. In April 2023, LangChain had incorporated and the new startup
      raised over $20 million in funding at a valuation of at least $200 million from
      venture firm Sequoia Capital, a week after announcing a $10 million seed investment
      from Benchmark.[3][4]\\n The project quickly garnered popularity, with improvements
      from hundreds of contributors on GitHub, trending discussions on Twitter, lively
      activity on the project''s Discord server, many YouTube tutorials, and meetups
      in San Francisco and London. As of April 2023, it can read from more than 50
      document types and data sources.[9]\\nReferences[edit]\\nExternal links[edit]\"},
      {\"url\": \"https://www.forbes.com/companies/langchain/\", \"content\": \"LangChain.
      Founded in late 2022, LangChain was an instant hit in Silicon Valley for its
      software that enables anyone to build apps powered by large language models
      like GPT-4 in as little as two ...\"}, {\"url\": \"https://tedai-sanfrancisco.ted.com/speakers-1/harrison-chase-/co-founder-and-ceo,-langchain/\",
      \"content\": \"Harrison Chase, a Harvard graduate in statistics and computer
      science, co-founded LangChain to streamline the development of Language Model
      applications with open-source Python/Typescript packages. Chase''s experience
      includes heading the Machine Learning team at Robust Intelligence, focusing
      on the testing and validation of machine learning models, and leading the entity
      linking team at Kensho ...\"}, {\"url\": \"https://www.crunchbase.com/organization/langchain\",
      \"content\": \"Summary. LangChain may be growing as evidenced by securing $25
      million in funding, which is a significant financial milestone for the company.
      Additionally, the launch of a new platform to facilitate the full lifecycle
      of Large Language Model applications suggests expansion in their product offerings.
      The integration of Gemini Pro API, enabling ...\"}, {\"url\": \"https://craft.co/langchain\",
      \"content\": \"LangChain has 5 employees at their 1 location and $35 m in total
      funding,. See insights on LangChain including office locations, competitors,
      revenue, financials, executives, subsidiaries and more at Craft. ... Harrison
      Chase, Co-Founder and Chief Executive Officer. Linkedin;\"}, {\"url\": \"https://www.langchain.com/about\",
      \"content\": \"Ready to start shipping\\nreliable GenAI apps faster?\\nLangChain
      and LangSmith are critical parts of the reference\\narchitecture to get you
      from prototype to production. Announcing the General Availability of LangSmith\\nToday,
      we\\u2019re thrilled to announce the general availability of LangSmith \\u2014
      our solution for LLM application development, monitoring, and testing.\\n We
      build products that enable developers to go from an idea to working code in
      an afternoon and in the hands of users in days or weeks. The biggest developer
      community in \\u00a0GenAI\\nLearn alongside the 100K+ practitioners\\nwho are
      pushing the industry forward.\\n And we built LangSmith to support all stages
      of the AI engineering lifecycle, to get applications into production faster.\\n\"},
      {\"url\": \"https://www.bluelabellabs.com/blog/what-is-langchain/\", \"content\":
      \"Similar setups to AutoTweeter would allow businesses to parse past and present
      customer queries to understand drop-off points and build the appropriate flows
      to solve customer problems on the spot, instead of spitting them into a queue
      to wait for a human and plot how they\\u2019re going to steal from your business.\\n
      Rather than creating a bot to tweet in your tone on your behalf about your love
      for processed snacks, hockey, and political happenings, you could train such
      a system on your brand voice, knowledge bases, and other resources to create
      a highly valuable feature for your digital touchpoints.\\n Here, you essentially
      create a curated ML system that\\u2019s tailored to your brand with specific
      issues and requests from your customers \\u2013 with a little bit of dev magic,
      you can quickly build a solution that integrates features like payment systems,
      tech support, ordering, account management, and so much more.\\n Creating a
      bot for social media that can post just like you\\nSocial media already has
      enough bots engaging with people as if they\\u2019re real people, but for the
      sake of making a functional example, our co-founder Bobby created a bot called
      AutoTweeter for the social media platform we all still call Twitter.\\n How
      does LangChain work?\\nLangchain essentially serves as a middleman between other
      LLMs, data sources, and API-accessible functions \\u2013 more precisely, it\\u2019s
      a comprehensive open-source library of components that allow users to build
      better (i.e., more custom) solutions by interconnecting different resources.\\n\"},
      {\"url\": \"https://www.latent.space/p/langchain\", \"content\": \"And so you
      can do a lot of things like reflection and kind of like making sure that you''re
      on the right track and really put more safeguards and thinking about these agents
      as opposed to relying on like chas and interface, like the bot we have that''s
      on GitHub answering questions on the issues, I think probably gives better answers
      than the bots that we have that are on chat on the website. [00:50:32]\\nHarrison:
      And then between kind of like allowing for, I think one helpful thing that we
      did in terms of like distinguishing between basically the base interfaces and
      then more complex stuff is part of the separation around the docs is there''s
      like the components piece, which has the model IO, the retrieval, the agents,
      the callbacks, things like that. And then like, I know a lot of people are doing
      this, but like a personal assistant for like managing like email calendar, basic
      stuff, which I think is, I think that''s like a fantastic application for these
      like agent like things, because if you think about personal assistants today,
      you usually interact, I don''t have one, but I''m told you interact with them
      over email. Show Notes\\nLangChain\\nLangChain\\u2019s Berkshire Hathaway Homepage\\nAbstractions
      tweet\\nLangSmith\\nLangSmith Cookbooks repo\\nLangChain Retrieval blog\\nEvaluating
      CSV Question/Answering blog and YouTube\\nMultiOn Partner blog\\nHarvard Sports
      Analytics Collective\\nEvaluating RAG Webinar\\nawesome-langchain:\\nLLM Math
      Chain\\nSelf-Ask\\nLangChain Hub UI\\n\\u201cLangChain is Pointless\\u201d\\nHarrison\\u2019s
      links\\nsports - estimating player compatibility in the NBA\\nearly interest
      in prompt injections\\nGitHub\\nTwitter\\nTimestamps\\n[00:00:00] Introduction\\n[00:00:48]
      Harrison''s background and how sports led him into ML\\n[00:04:54] Memory (for
      long running chats, whether a simple Buffer, Knowledge Graph, Summary, or Vector
      Store)\\nUse-Cases: compositions of Components\\nChains: combining a PromptTemplate,
      LLM Model and optional OutputParser\\nwith Router, Sequential, and Transform
      Chains for advanced usecases\\nsavable, sharable schemas that can be loaded
      from LangChainHub\\nAgents: a chain that has access to a suite of tools, of
      nondeterministic8 length because the LLM is used as a reasoning engine to determine
      which actions to take and in which order.\"}, {\"url\": \"https://www.langchain.com/\",
      \"content\": \"Join Discord\\nContribute on GitHub\\n5.3M\\nMonthly Downloads\\n67k\\nGithub
      Stars\\n1800+\\nContributors\\n34k+\\nApps Powered\\nReady to build?\\nGo To
      Docs\\nGo To Docs\\nGo To Docs\\nContact Sales\\nContact Sales\\nContact Sales\\nProducts
      and use-cases\\nLangChain\\nLangSmith\\nRetrieval\\nAgents\\nInspiration\\nCode\\nGitHub\\nLangChain
      Hub\\nPython Docs\\nJS/TS Docs\\nSocial\\nTwitter\\nDiscord\\nBlog\\nLinkedIn\\nYouTube\\nTerms
      of Service\\nSign up for our newsletter\\nProducts and use-cases\\nLangChain\\nLangSmith\\nRetrieval\\nAgents\\nInspiration\\nCode\\nGitHub\\nLangChain
      Hub\\nPython Docs\\nJS/TS Docs\\nSocial\\nTwitter\\nDiscord\\nBlog\\nLinkedIn\\nYouTube\\nTerms
      of Service\\nSign up for our newsletter\\nProducts and use-cases\\nLangChain\\nLangSmith\\nRetrieval\\nAgents\\nInspiration\\nCode\\nGitHub\\nLangChain
      Hub\\nPython Docs\\nJS/TS Docs\\nSocial\\nTwitter\\nDiscord\\nBlog\\nLinkedIn\\nYouTube\\nTerms
      of Service\\nSign up for our newsletter Chatbots\\nQ&A Over Docs\\nSummarization\\nCopilots\\nWorkflow
      Automation\\nDocument Analysis\\nCustom Search\\nHarness the power\\u2013and
      wrangle the complexity\\u2013 of LLMs\\nGet from proof-of-concept to production
      on one platform\\nRely on a set of tools built especially for non-deterministic
      models\\nLeverage new cognitive architectures and battle-tested orchestration\\nThanks
      to our contributors and partners for making LangChain awesome\\nWhen you build
      with LangChain, you build alongside a community of developers advancing the
      frontier of what\\u2019s possible with LLMs.\\n \\ud83e\\udd9c\\ud83d\\udd17
      LangChain\\nLangSmith\\nLangServe\\nAgents\\nRetrieval\\nEvaluation\\nBlog\\nDocs\\n\\ud83e\\udd9c\\ud83d\\udd17
      LangChain\\n\\ud83e\\udd9c\\ud83d\\udd17 LangChain\\nLangChain Expression Language
      makes it easy to create custom chains.\\n Go To Docs\\nGo To Docs\\nGo To Docs\\nContact
      Sales\\nglobal corporations, STARTUPS, and TINKERERS build with LangChain\\nglobal
      corporations, STARTUPS, and TINKERERS build with LangChain\\nglobal corporations,
      STARTUPS, and TINKERERS build with LangChain\\nA complete set of powerful building
      blocks\\nA complete set of powerful building blocks\\n Try it out here\\nGet
      your LLM application from prototype to production\\nGet your LLM application
      from prototype to production\\nBuild context-aware, reasoning applications with
      LangChain\\u2019s flexible abstractions and AI-first toolkit.\\n\"}]", "tool_use_id":
      "toolu_01JtnSAfQidha7JoYCXqUYzc", "is_error": false}]}], "model": "claude-3-5-sonnet-20240620",
      "tool_choice": {"type": "any"}, "tools": [{"name": "scrape_website", "description":
      "Scrape and summarize content from a given URL.", "input_schema": {"type": "object",
      "properties": {"url": {"type": "string"}}, "required": ["url"]}}, {"name": "search",
      "description": "Search for general results. This function performs a search
      using the Tavily search engine, which is designed\nto provide comprehensive,
      accurate, and trusted results. It''s particularly useful\nfor answering questions
      about current events.", "input_schema": {"type": "object", "properties": {"query":
      {"type": "string"}}, "required": ["query"]}}, {"name": "Info", "description":
      "Call this when you have gathered all the relevant info", "input_schema": {"type":
      "object", "properties": {"founder": {"type": "string", "description": "The name
      of the company founder."}, "websiteUrl": {"type": "string", "description": "Website
      URL of the company, e.g.: https://openai.com/, or https://microsoft.com"}, "products_sold":
      {"type": "array", "items": {"type": "string"}, "description": "A list of products
      sold by the company."}}, "required": ["founder", "websiteUrl", "products_sold"]}}]}'
    headers: {}
    method: POST
    uri: https://api.anthropic.com/v1/messages
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA1SP3UrDQBCF32WuN81P21j3TigtFBGxiKhI2CZjG7KZTTOzRBvy7pJIL3o5Z+ac
        +U4PZQEaaj5mUZzstunm4523fr9cn6rvx2q3whQUyG+D4xUymyOCgtbZUTDMJYshAQW1K9CChtwa
        X2AwD5YBOyKUIImSRZQmESjIHQmSgP7sr6HinM08j6kTyjj7LIpXL2d6Pcx/0uXlbXP/vF9fHpIn
        BgVk6tHHeWsazDo8cCmTmxovoHvw7chxEmlYh2HXdTNr6JifTEmz3NUhDMOXAhbXZC0adnRLMS0Y
        zx4pR9DkrVXgp+K6//+SiauQGPQijhMFzsuNeDcMfwAAAP//AwAZCFJOWAEAAA==
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c4af82f7cc9fa72-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 17 Sep 2024 17:55:52 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Robots-Tag:
      - none
      anthropic-ratelimit-requests-limit:
      - '4000'
      anthropic-ratelimit-requests-remaining:
      - '3999'
      anthropic-ratelimit-requests-reset:
      - '2024-09-17T17:55:54Z'
      anthropic-ratelimit-tokens-limit:
      - '400000'
      anthropic-ratelimit-tokens-remaining:
      - '400000'
      anthropic-ratelimit-tokens-reset:
      - '2024-09-17T17:55:52Z'
      request-id:
      - req_0112C84Bvx9Tnzv4NTYA8K4t
      via:
      - 1.1 google
      x-cloud-trace-context:
      - efac6bac769f8e88ae6445e24b167f86
    status:
      code: 200
      message: OK
- request:
    body: '{"max_tokens": 1024, "messages": [{"role": "user", "content": "You are
      doing web research on behalf of a user. You are trying to figure out this information:\n\n<info>\n{\n  \"type\":
      \"object\",\n  \"properties\": {\n    \"founder\": {\n      \"type\": \"string\",\n      \"description\":
      \"The name of the company founder.\"\n    },\n    \"websiteUrl\": {\n      \"type\":
      \"string\",\n      \"description\": \"Website URL of the company, e.g.: https://openai.com/,
      or https://microsoft.com\"\n    },\n    \"products_sold\": {\n      \"type\":
      \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      },\n      \"description\":
      \"A list of products sold by the company.\"\n    }\n  },\n  \"required\": [\n    \"founder\",\n    \"websiteUrl\",\n    \"products_sold\"\n  ]\n}\n</info>\n\nYou
      have access to the following tools:\n\n- `Search`: call a search tool and get
      back some results\n- `ScrapeWebsite`: scrape a website and get relevant notes
      about the given request. This will update the notes above.\n- `Info`: call this
      when you are done and have gathered all the relevant info\n\nHere is the information
      you have about the topic you are researching:\n\nTopic: LangChain"}, {"role":
      "assistant", "content": [{"type": "tool_use", "name": "search", "input": {"query":
      "LangChain company founder, website, and products"}, "id": "toolu_01JtnSAfQidha7JoYCXqUYzc"}]},
      {"role": "user", "content": [{"type": "tool_result", "content": "[{\"url\":
      \"https://www.cbinsights.com/company/langchain\", \"content\": \"It was founded
      in 2023 and is based in San Francisco, California. LangChain specializes in
      the development of large language model (LLM) applications, providing a suite
      of products that support developers throughout the application lifecycle. Use
      the CB Insights Platform to explore LangChain''s full profile.\"}, {\"url\":
      \"https://en.wikipedia.org/wiki/LangChain\", \"content\": \"In October 2023
      LangChain introduced LangServe, a deployment tool designed to facilitate the
      transition from LCEL (LangChain Expression Language) prototypes to production-ready
      applications.[5]\\nIntegrations[edit]\\nAs of March 2023, LangChain included
      integrations with systems including Amazon, Google, and Microsoft Azure cloud
      storage; API wrappers for news, movie information, and weather; Bash for summarization,
      syntax and semantics checking, and execution of shell scripts; multiple web
      scraping subsystems and templates; few-shot learning prompt generation support;
      finding and summarizing \\\"todo\\\" tasks in code; Google Drive documents,
      spreadsheets, and presentations summarization, extraction, and creation; Google
      Search and Microsoft Bing web search; OpenAI, Anthropic, and Hugging Face language
      models; iFixit repair guides and wikis search and summarization; MapReduce for
      question answering, combining documents, and question generation; N-gram overlap
      scoring; PyPDF, pdfminer, fitz, and pymupdf for PDF file text extraction and
      manipulation; Python and JavaScript code generation, analysis, and debugging;
      Milvus vector database[6] to store and retrieve vector embeddings; Weaviate
      vector database[7] to cache embedding and data objects; Redis cache database
      storage; Python RequestsWrapper and other methods for API requests; SQL and
      NoSQL databases including JSON support; Streamlit, including for logging; text
      mapping for k-nearest neighbors search; time zone conversion and calendar operations;
      tracing and recording stack symbols in threaded and asynchronous subprocess
      runs; and the Wolfram Alpha website and SDK.[8] As a language model integration
      framework, LangChain''s use-cases largely overlap with those of language models
      in general, including document analysis and summarization, chatbots, and code
      analysis.[2]\\nHistory[edit]\\nLangChain was launched in October 2022 as an
      open source project by Harrison Chase, while working at machine learning startup
      Robust Intelligence. In April 2023, LangChain had incorporated and the new startup
      raised over $20 million in funding at a valuation of at least $200 million from
      venture firm Sequoia Capital, a week after announcing a $10 million seed investment
      from Benchmark.[3][4]\\n The project quickly garnered popularity, with improvements
      from hundreds of contributors on GitHub, trending discussions on Twitter, lively
      activity on the project''s Discord server, many YouTube tutorials, and meetups
      in San Francisco and London. As of April 2023, it can read from more than 50
      document types and data sources.[9]\\nReferences[edit]\\nExternal links[edit]\"},
      {\"url\": \"https://www.forbes.com/companies/langchain/\", \"content\": \"LangChain.
      Founded in late 2022, LangChain was an instant hit in Silicon Valley for its
      software that enables anyone to build apps powered by large language models
      like GPT-4 in as little as two ...\"}, {\"url\": \"https://tedai-sanfrancisco.ted.com/speakers-1/harrison-chase-/co-founder-and-ceo,-langchain/\",
      \"content\": \"Harrison Chase, a Harvard graduate in statistics and computer
      science, co-founded LangChain to streamline the development of Language Model
      applications with open-source Python/Typescript packages. Chase''s experience
      includes heading the Machine Learning team at Robust Intelligence, focusing
      on the testing and validation of machine learning models, and leading the entity
      linking team at Kensho ...\"}, {\"url\": \"https://www.crunchbase.com/organization/langchain\",
      \"content\": \"Summary. LangChain may be growing as evidenced by securing $25
      million in funding, which is a significant financial milestone for the company.
      Additionally, the launch of a new platform to facilitate the full lifecycle
      of Large Language Model applications suggests expansion in their product offerings.
      The integration of Gemini Pro API, enabling ...\"}, {\"url\": \"https://craft.co/langchain\",
      \"content\": \"LangChain has 5 employees at their 1 location and $35 m in total
      funding,. See insights on LangChain including office locations, competitors,
      revenue, financials, executives, subsidiaries and more at Craft. ... Harrison
      Chase, Co-Founder and Chief Executive Officer. Linkedin;\"}, {\"url\": \"https://www.langchain.com/about\",
      \"content\": \"Ready to start shipping\\nreliable GenAI apps faster?\\nLangChain
      and LangSmith are critical parts of the reference\\narchitecture to get you
      from prototype to production. Announcing the General Availability of LangSmith\\nToday,
      we\\u2019re thrilled to announce the general availability of LangSmith \\u2014
      our solution for LLM application development, monitoring, and testing.\\n We
      build products that enable developers to go from an idea to working code in
      an afternoon and in the hands of users in days or weeks. The biggest developer
      community in \\u00a0GenAI\\nLearn alongside the 100K+ practitioners\\nwho are
      pushing the industry forward.\\n And we built LangSmith to support all stages
      of the AI engineering lifecycle, to get applications into production faster.\\n\"},
      {\"url\": \"https://www.bluelabellabs.com/blog/what-is-langchain/\", \"content\":
      \"Similar setups to AutoTweeter would allow businesses to parse past and present
      customer queries to understand drop-off points and build the appropriate flows
      to solve customer problems on the spot, instead of spitting them into a queue
      to wait for a human and plot how they\\u2019re going to steal from your business.\\n
      Rather than creating a bot to tweet in your tone on your behalf about your love
      for processed snacks, hockey, and political happenings, you could train such
      a system on your brand voice, knowledge bases, and other resources to create
      a highly valuable feature for your digital touchpoints.\\n Here, you essentially
      create a curated ML system that\\u2019s tailored to your brand with specific
      issues and requests from your customers \\u2013 with a little bit of dev magic,
      you can quickly build a solution that integrates features like payment systems,
      tech support, ordering, account management, and so much more.\\n Creating a
      bot for social media that can post just like you\\nSocial media already has
      enough bots engaging with people as if they\\u2019re real people, but for the
      sake of making a functional example, our co-founder Bobby created a bot called
      AutoTweeter for the social media platform we all still call Twitter.\\n How
      does LangChain work?\\nLangchain essentially serves as a middleman between other
      LLMs, data sources, and API-accessible functions \\u2013 more precisely, it\\u2019s
      a comprehensive open-source library of components that allow users to build
      better (i.e., more custom) solutions by interconnecting different resources.\\n\"},
      {\"url\": \"https://www.latent.space/p/langchain\", \"content\": \"And so you
      can do a lot of things like reflection and kind of like making sure that you''re
      on the right track and really put more safeguards and thinking about these agents
      as opposed to relying on like chas and interface, like the bot we have that''s
      on GitHub answering questions on the issues, I think probably gives better answers
      than the bots that we have that are on chat on the website. [00:50:32]\\nHarrison:
      And then between kind of like allowing for, I think one helpful thing that we
      did in terms of like distinguishing between basically the base interfaces and
      then more complex stuff is part of the separation around the docs is there''s
      like the components piece, which has the model IO, the retrieval, the agents,
      the callbacks, things like that. And then like, I know a lot of people are doing
      this, but like a personal assistant for like managing like email calendar, basic
      stuff, which I think is, I think that''s like a fantastic application for these
      like agent like things, because if you think about personal assistants today,
      you usually interact, I don''t have one, but I''m told you interact with them
      over email. Show Notes\\nLangChain\\nLangChain\\u2019s Berkshire Hathaway Homepage\\nAbstractions
      tweet\\nLangSmith\\nLangSmith Cookbooks repo\\nLangChain Retrieval blog\\nEvaluating
      CSV Question/Answering blog and YouTube\\nMultiOn Partner blog\\nHarvard Sports
      Analytics Collective\\nEvaluating RAG Webinar\\nawesome-langchain:\\nLLM Math
      Chain\\nSelf-Ask\\nLangChain Hub UI\\n\\u201cLangChain is Pointless\\u201d\\nHarrison\\u2019s
      links\\nsports - estimating player compatibility in the NBA\\nearly interest
      in prompt injections\\nGitHub\\nTwitter\\nTimestamps\\n[00:00:00] Introduction\\n[00:00:48]
      Harrison''s background and how sports led him into ML\\n[00:04:54] Memory (for
      long running chats, whether a simple Buffer, Knowledge Graph, Summary, or Vector
      Store)\\nUse-Cases: compositions of Components\\nChains: combining a PromptTemplate,
      LLM Model and optional OutputParser\\nwith Router, Sequential, and Transform
      Chains for advanced usecases\\nsavable, sharable schemas that can be loaded
      from LangChainHub\\nAgents: a chain that has access to a suite of tools, of
      nondeterministic8 length because the LLM is used as a reasoning engine to determine
      which actions to take and in which order.\"}, {\"url\": \"https://www.langchain.com/\",
      \"content\": \"Join Discord\\nContribute on GitHub\\n5.3M\\nMonthly Downloads\\n67k\\nGithub
      Stars\\n1800+\\nContributors\\n34k+\\nApps Powered\\nReady to build?\\nGo To
      Docs\\nGo To Docs\\nGo To Docs\\nContact Sales\\nContact Sales\\nContact Sales\\nProducts
      and use-cases\\nLangChain\\nLangSmith\\nRetrieval\\nAgents\\nInspiration\\nCode\\nGitHub\\nLangChain
      Hub\\nPython Docs\\nJS/TS Docs\\nSocial\\nTwitter\\nDiscord\\nBlog\\nLinkedIn\\nYouTube\\nTerms
      of Service\\nSign up for our newsletter\\nProducts and use-cases\\nLangChain\\nLangSmith\\nRetrieval\\nAgents\\nInspiration\\nCode\\nGitHub\\nLangChain
      Hub\\nPython Docs\\nJS/TS Docs\\nSocial\\nTwitter\\nDiscord\\nBlog\\nLinkedIn\\nYouTube\\nTerms
      of Service\\nSign up for our newsletter\\nProducts and use-cases\\nLangChain\\nLangSmith\\nRetrieval\\nAgents\\nInspiration\\nCode\\nGitHub\\nLangChain
      Hub\\nPython Docs\\nJS/TS Docs\\nSocial\\nTwitter\\nDiscord\\nBlog\\nLinkedIn\\nYouTube\\nTerms
      of Service\\nSign up for our newsletter Chatbots\\nQ&A Over Docs\\nSummarization\\nCopilots\\nWorkflow
      Automation\\nDocument Analysis\\nCustom Search\\nHarness the power\\u2013and
      wrangle the complexity\\u2013 of LLMs\\nGet from proof-of-concept to production
      on one platform\\nRely on a set of tools built especially for non-deterministic
      models\\nLeverage new cognitive architectures and battle-tested orchestration\\nThanks
      to our contributors and partners for making LangChain awesome\\nWhen you build
      with LangChain, you build alongside a community of developers advancing the
      frontier of what\\u2019s possible with LLMs.\\n \\ud83e\\udd9c\\ud83d\\udd17
      LangChain\\nLangSmith\\nLangServe\\nAgents\\nRetrieval\\nEvaluation\\nBlog\\nDocs\\n\\ud83e\\udd9c\\ud83d\\udd17
      LangChain\\n\\ud83e\\udd9c\\ud83d\\udd17 LangChain\\nLangChain Expression Language
      makes it easy to create custom chains.\\n Go To Docs\\nGo To Docs\\nGo To Docs\\nContact
      Sales\\nglobal corporations, STARTUPS, and TINKERERS build with LangChain\\nglobal
      corporations, STARTUPS, and TINKERERS build with LangChain\\nglobal corporations,
      STARTUPS, and TINKERERS build with LangChain\\nA complete set of powerful building
      blocks\\nA complete set of powerful building blocks\\n Try it out here\\nGet
      your LLM application from prototype to production\\nGet your LLM application
      from prototype to production\\nBuild context-aware, reasoning applications with
      LangChain\\u2019s flexible abstractions and AI-first toolkit.\\n\"}]", "tool_use_id":
      "toolu_01JtnSAfQidha7JoYCXqUYzc", "is_error": false}]}, {"role": "assistant",
      "content": [{"type": "tool_use", "name": "scrape_website", "input": {"url":
      "https://www.langchain.com/"}, "id": "toolu_018RqnUb3x65zWF9PSDzA2Ns"}]}, {"role":
      "user", "content": [{"type": "tool_result", "content": "Here are some key notes
      about the LangChain website based on the content:\n\n1. LangChain offers a suite
      of products for building, running, and managing LLM applications:\n   - LangChain:
      A framework for building with LLMs\n   - LangGraph: A framework for building
      controllable agentic workflows\n   - LangSmith: A platform for debugging, testing,
      deploying and monitoring LLM apps\n\n2. The website highlights that LangChain
      products can be used independently or together.\n\n3. Key stats mentioned:\n   -
      15M+ monthly downloads\n   - 100K+ apps powered\n   - 75K+ GitHub stars\n   -
      3K+ contributors\n\n4. The site emphasizes LangChain''s large developer community
      (1M+ developers).\n\n5. There are testimonials from companies like Retool, Rakuten,
      and Elastic about using LangChain products.\n\n6. The website offers resources
      like documentation, GitHub links, blog, and community pages.\n\n7. There''s
      a newsletter signup form in the footer.\n\n8. The site promotes both free signup
      and enterprise demos.\n\n9. Use cases across industries like financial services,
      fintech, and technology are highlighted.\n\n10. The website design uses a lot
      of green coloring, matching LangChain''s branding.", "tool_use_id": "toolu_018RqnUb3x65zWF9PSDzA2Ns",
      "is_error": false}]}], "model": "claude-3-5-sonnet-20240620", "tool_choice":
      {"type": "any"}, "tools": [{"name": "scrape_website", "description": "Scrape
      and summarize content from a given URL.", "input_schema": {"type": "object",
      "properties": {"url": {"type": "string"}}, "required": ["url"]}}, {"name": "search",
      "description": "Search for general results. This function performs a search
      using the Tavily search engine, which is designed\nto provide comprehensive,
      accurate, and trusted results. It''s particularly useful\nfor answering questions
      about current events.", "input_schema": {"type": "object", "properties": {"query":
      {"type": "string"}}, "required": ["query"]}}, {"name": "Info", "description":
      "Call this when you have gathered all the relevant info", "input_schema": {"type":
      "object", "properties": {"founder": {"type": "string", "description": "The name
      of the company founder."}, "websiteUrl": {"type": "string", "description": "Website
      URL of the company, e.g.: https://openai.com/, or https://microsoft.com"}, "products_sold":
      {"type": "array", "items": {"type": "string"}, "description": "A list of products
      sold by the company."}}, "required": ["founder", "websiteUrl", "products_sold"]}}]}'
    headers: {}
    method: POST
    uri: https://api.anthropic.com/v1/messages
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA1RQ0UrDQBD8FdnnS5vGVuy9aSlG24JgtQ+lhEvumgQve/H2jlRC/l0uKujb7OzM
        MLs91BI4NFRm8Wzx8vZ0kYc277b3u7pMVoJKImDgPlsVVIpIlAoYWKMDIYhqcgIdMGiMVBo4FFp4
        qaLraBGRQVQuSuJkHt8kMTAoDDqFDvix/w11xujMU0gdq4TZZ/Hs0O7yVO736/Ri73JK5eY5WW+A
        AYom+B7xbIIHW++A93A2HqWywCEV1tZk8GpViTG3UznVTr3a0K9yriU+nXZdN9ECy6ISNU4K00yB
        QWuN9IWjjIyWwI+wFViuggLYiB+saKsf/NLUroLTMJwYkDNtZpUgg/9vGhekPrzCQgFHrzUDP76R
        99/tM2feFRLw+fw2ZmC8+0sul8PwBQAA//8DADLSBIWmAQAA
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c4af83c19ca9e50-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 17 Sep 2024 17:55:54 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Robots-Tag:
      - none
      anthropic-ratelimit-requests-limit:
      - '4000'
      anthropic-ratelimit-requests-remaining:
      - '3999'
      anthropic-ratelimit-requests-reset:
      - '2024-09-17T17:56:54Z'
      anthropic-ratelimit-tokens-limit:
      - '400000'
      anthropic-ratelimit-tokens-remaining:
      - '400000'
      anthropic-ratelimit-tokens-reset:
      - '2024-09-17T17:55:54Z'
      request-id:
      - req_01MdGZEXErzbkdgwN1gdHZ3f
      via:
      - 1.1 google
      x-cloud-trace-context:
      - 609e1261acac28312845cf27b91fb6e0
    status:
      code: 200
      message: OK
- request:
    body: '{"max_tokens": 1024, "messages": [{"role": "user", "content": "You are
      doing web research on behalf of a user. You are trying to figure out this information:\n\n<info>\n{\n  \"type\":
      \"object\",\n  \"properties\": {\n    \"founder\": {\n      \"type\": \"string\",\n      \"description\":
      \"The name of the company founder.\"\n    },\n    \"websiteUrl\": {\n      \"type\":
      \"string\",\n      \"description\": \"Website URL of the company, e.g.: https://openai.com/,
      or https://microsoft.com\"\n    },\n    \"products_sold\": {\n      \"type\":
      \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      },\n      \"description\":
      \"A list of products sold by the company.\"\n    }\n  },\n  \"required\": [\n    \"founder\",\n    \"websiteUrl\",\n    \"products_sold\"\n  ]\n}\n</info>\n\nYou
      have access to the following tools:\n\n- `Search`: call a search tool and get
      back some results\n- `ScrapeWebsite`: scrape a website and get relevant notes
      about the given request. This will update the notes above.\n- `Info`: call this
      when you are done and have gathered all the relevant info\n\nHere is the information
      you have about the topic you are researching:\n\nTopic: LangChain"}, {"role":
      "assistant", "content": [{"type": "tool_use", "name": "search", "input": {"query":
      "LangChain company founder, website, and products"}, "id": "toolu_01JtnSAfQidha7JoYCXqUYzc"}]},
      {"role": "user", "content": [{"type": "tool_result", "content": "[{\"url\":
      \"https://www.cbinsights.com/company/langchain\", \"content\": \"It was founded
      in 2023 and is based in San Francisco, California. LangChain specializes in
      the development of large language model (LLM) applications, providing a suite
      of products that support developers throughout the application lifecycle. Use
      the CB Insights Platform to explore LangChain''s full profile.\"}, {\"url\":
      \"https://en.wikipedia.org/wiki/LangChain\", \"content\": \"In October 2023
      LangChain introduced LangServe, a deployment tool designed to facilitate the
      transition from LCEL (LangChain Expression Language) prototypes to production-ready
      applications.[5]\\nIntegrations[edit]\\nAs of March 2023, LangChain included
      integrations with systems including Amazon, Google, and Microsoft Azure cloud
      storage; API wrappers for news, movie information, and weather; Bash for summarization,
      syntax and semantics checking, and execution of shell scripts; multiple web
      scraping subsystems and templates; few-shot learning prompt generation support;
      finding and summarizing \\\"todo\\\" tasks in code; Google Drive documents,
      spreadsheets, and presentations summarization, extraction, and creation; Google
      Search and Microsoft Bing web search; OpenAI, Anthropic, and Hugging Face language
      models; iFixit repair guides and wikis search and summarization; MapReduce for
      question answering, combining documents, and question generation; N-gram overlap
      scoring; PyPDF, pdfminer, fitz, and pymupdf for PDF file text extraction and
      manipulation; Python and JavaScript code generation, analysis, and debugging;
      Milvus vector database[6] to store and retrieve vector embeddings; Weaviate
      vector database[7] to cache embedding and data objects; Redis cache database
      storage; Python RequestsWrapper and other methods for API requests; SQL and
      NoSQL databases including JSON support; Streamlit, including for logging; text
      mapping for k-nearest neighbors search; time zone conversion and calendar operations;
      tracing and recording stack symbols in threaded and asynchronous subprocess
      runs; and the Wolfram Alpha website and SDK.[8] As a language model integration
      framework, LangChain''s use-cases largely overlap with those of language models
      in general, including document analysis and summarization, chatbots, and code
      analysis.[2]\\nHistory[edit]\\nLangChain was launched in October 2022 as an
      open source project by Harrison Chase, while working at machine learning startup
      Robust Intelligence. In April 2023, LangChain had incorporated and the new startup
      raised over $20 million in funding at a valuation of at least $200 million from
      venture firm Sequoia Capital, a week after announcing a $10 million seed investment
      from Benchmark.[3][4]\\n The project quickly garnered popularity, with improvements
      from hundreds of contributors on GitHub, trending discussions on Twitter, lively
      activity on the project''s Discord server, many YouTube tutorials, and meetups
      in San Francisco and London. As of April 2023, it can read from more than 50
      document types and data sources.[9]\\nReferences[edit]\\nExternal links[edit]\"},
      {\"url\": \"https://www.forbes.com/companies/langchain/\", \"content\": \"LangChain.
      Founded in late 2022, LangChain was an instant hit in Silicon Valley for its
      software that enables anyone to build apps powered by large language models
      like GPT-4 in as little as two ...\"}, {\"url\": \"https://tedai-sanfrancisco.ted.com/speakers-1/harrison-chase-/co-founder-and-ceo,-langchain/\",
      \"content\": \"Harrison Chase, a Harvard graduate in statistics and computer
      science, co-founded LangChain to streamline the development of Language Model
      applications with open-source Python/Typescript packages. Chase''s experience
      includes heading the Machine Learning team at Robust Intelligence, focusing
      on the testing and validation of machine learning models, and leading the entity
      linking team at Kensho ...\"}, {\"url\": \"https://www.crunchbase.com/organization/langchain\",
      \"content\": \"Summary. LangChain may be growing as evidenced by securing $25
      million in funding, which is a significant financial milestone for the company.
      Additionally, the launch of a new platform to facilitate the full lifecycle
      of Large Language Model applications suggests expansion in their product offerings.
      The integration of Gemini Pro API, enabling ...\"}, {\"url\": \"https://craft.co/langchain\",
      \"content\": \"LangChain has 5 employees at their 1 location and $35 m in total
      funding,. See insights on LangChain including office locations, competitors,
      revenue, financials, executives, subsidiaries and more at Craft. ... Harrison
      Chase, Co-Founder and Chief Executive Officer. Linkedin;\"}, {\"url\": \"https://www.langchain.com/about\",
      \"content\": \"Ready to start shipping\\nreliable GenAI apps faster?\\nLangChain
      and LangSmith are critical parts of the reference\\narchitecture to get you
      from prototype to production. Announcing the General Availability of LangSmith\\nToday,
      we\\u2019re thrilled to announce the general availability of LangSmith \\u2014
      our solution for LLM application development, monitoring, and testing.\\n We
      build products that enable developers to go from an idea to working code in
      an afternoon and in the hands of users in days or weeks. The biggest developer
      community in \\u00a0GenAI\\nLearn alongside the 100K+ practitioners\\nwho are
      pushing the industry forward.\\n And we built LangSmith to support all stages
      of the AI engineering lifecycle, to get applications into production faster.\\n\"},
      {\"url\": \"https://www.bluelabellabs.com/blog/what-is-langchain/\", \"content\":
      \"Similar setups to AutoTweeter would allow businesses to parse past and present
      customer queries to understand drop-off points and build the appropriate flows
      to solve customer problems on the spot, instead of spitting them into a queue
      to wait for a human and plot how they\\u2019re going to steal from your business.\\n
      Rather than creating a bot to tweet in your tone on your behalf about your love
      for processed snacks, hockey, and political happenings, you could train such
      a system on your brand voice, knowledge bases, and other resources to create
      a highly valuable feature for your digital touchpoints.\\n Here, you essentially
      create a curated ML system that\\u2019s tailored to your brand with specific
      issues and requests from your customers \\u2013 with a little bit of dev magic,
      you can quickly build a solution that integrates features like payment systems,
      tech support, ordering, account management, and so much more.\\n Creating a
      bot for social media that can post just like you\\nSocial media already has
      enough bots engaging with people as if they\\u2019re real people, but for the
      sake of making a functional example, our co-founder Bobby created a bot called
      AutoTweeter for the social media platform we all still call Twitter.\\n How
      does LangChain work?\\nLangchain essentially serves as a middleman between other
      LLMs, data sources, and API-accessible functions \\u2013 more precisely, it\\u2019s
      a comprehensive open-source library of components that allow users to build
      better (i.e., more custom) solutions by interconnecting different resources.\\n\"},
      {\"url\": \"https://www.latent.space/p/langchain\", \"content\": \"And so you
      can do a lot of things like reflection and kind of like making sure that you''re
      on the right track and really put more safeguards and thinking about these agents
      as opposed to relying on like chas and interface, like the bot we have that''s
      on GitHub answering questions on the issues, I think probably gives better answers
      than the bots that we have that are on chat on the website. [00:50:32]\\nHarrison:
      And then between kind of like allowing for, I think one helpful thing that we
      did in terms of like distinguishing between basically the base interfaces and
      then more complex stuff is part of the separation around the docs is there''s
      like the components piece, which has the model IO, the retrieval, the agents,
      the callbacks, things like that. And then like, I know a lot of people are doing
      this, but like a personal assistant for like managing like email calendar, basic
      stuff, which I think is, I think that''s like a fantastic application for these
      like agent like things, because if you think about personal assistants today,
      you usually interact, I don''t have one, but I''m told you interact with them
      over email. Show Notes\\nLangChain\\nLangChain\\u2019s Berkshire Hathaway Homepage\\nAbstractions
      tweet\\nLangSmith\\nLangSmith Cookbooks repo\\nLangChain Retrieval blog\\nEvaluating
      CSV Question/Answering blog and YouTube\\nMultiOn Partner blog\\nHarvard Sports
      Analytics Collective\\nEvaluating RAG Webinar\\nawesome-langchain:\\nLLM Math
      Chain\\nSelf-Ask\\nLangChain Hub UI\\n\\u201cLangChain is Pointless\\u201d\\nHarrison\\u2019s
      links\\nsports - estimating player compatibility in the NBA\\nearly interest
      in prompt injections\\nGitHub\\nTwitter\\nTimestamps\\n[00:00:00] Introduction\\n[00:00:48]
      Harrison''s background and how sports led him into ML\\n[00:04:54] Memory (for
      long running chats, whether a simple Buffer, Knowledge Graph, Summary, or Vector
      Store)\\nUse-Cases: compositions of Components\\nChains: combining a PromptTemplate,
      LLM Model and optional OutputParser\\nwith Router, Sequential, and Transform
      Chains for advanced usecases\\nsavable, sharable schemas that can be loaded
      from LangChainHub\\nAgents: a chain that has access to a suite of tools, of
      nondeterministic8 length because the LLM is used as a reasoning engine to determine
      which actions to take and in which order.\"}, {\"url\": \"https://www.langchain.com/\",
      \"content\": \"Join Discord\\nContribute on GitHub\\n5.3M\\nMonthly Downloads\\n67k\\nGithub
      Stars\\n1800+\\nContributors\\n34k+\\nApps Powered\\nReady to build?\\nGo To
      Docs\\nGo To Docs\\nGo To Docs\\nContact Sales\\nContact Sales\\nContact Sales\\nProducts
      and use-cases\\nLangChain\\nLangSmith\\nRetrieval\\nAgents\\nInspiration\\nCode\\nGitHub\\nLangChain
      Hub\\nPython Docs\\nJS/TS Docs\\nSocial\\nTwitter\\nDiscord\\nBlog\\nLinkedIn\\nYouTube\\nTerms
      of Service\\nSign up for our newsletter\\nProducts and use-cases\\nLangChain\\nLangSmith\\nRetrieval\\nAgents\\nInspiration\\nCode\\nGitHub\\nLangChain
      Hub\\nPython Docs\\nJS/TS Docs\\nSocial\\nTwitter\\nDiscord\\nBlog\\nLinkedIn\\nYouTube\\nTerms
      of Service\\nSign up for our newsletter\\nProducts and use-cases\\nLangChain\\nLangSmith\\nRetrieval\\nAgents\\nInspiration\\nCode\\nGitHub\\nLangChain
      Hub\\nPython Docs\\nJS/TS Docs\\nSocial\\nTwitter\\nDiscord\\nBlog\\nLinkedIn\\nYouTube\\nTerms
      of Service\\nSign up for our newsletter Chatbots\\nQ&A Over Docs\\nSummarization\\nCopilots\\nWorkflow
      Automation\\nDocument Analysis\\nCustom Search\\nHarness the power\\u2013and
      wrangle the complexity\\u2013 of LLMs\\nGet from proof-of-concept to production
      on one platform\\nRely on a set of tools built especially for non-deterministic
      models\\nLeverage new cognitive architectures and battle-tested orchestration\\nThanks
      to our contributors and partners for making LangChain awesome\\nWhen you build
      with LangChain, you build alongside a community of developers advancing the
      frontier of what\\u2019s possible with LLMs.\\n \\ud83e\\udd9c\\ud83d\\udd17
      LangChain\\nLangSmith\\nLangServe\\nAgents\\nRetrieval\\nEvaluation\\nBlog\\nDocs\\n\\ud83e\\udd9c\\ud83d\\udd17
      LangChain\\n\\ud83e\\udd9c\\ud83d\\udd17 LangChain\\nLangChain Expression Language
      makes it easy to create custom chains.\\n Go To Docs\\nGo To Docs\\nGo To Docs\\nContact
      Sales\\nglobal corporations, STARTUPS, and TINKERERS build with LangChain\\nglobal
      corporations, STARTUPS, and TINKERERS build with LangChain\\nglobal corporations,
      STARTUPS, and TINKERERS build with LangChain\\nA complete set of powerful building
      blocks\\nA complete set of powerful building blocks\\n Try it out here\\nGet
      your LLM application from prototype to production\\nGet your LLM application
      from prototype to production\\nBuild context-aware, reasoning applications with
      LangChain\\u2019s flexible abstractions and AI-first toolkit.\\n\"}]", "tool_use_id":
      "toolu_01JtnSAfQidha7JoYCXqUYzc", "is_error": false}]}, {"role": "assistant",
      "content": [{"type": "tool_use", "name": "scrape_website", "input": {"url":
      "https://www.langchain.com/"}, "id": "toolu_018RqnUb3x65zWF9PSDzA2Ns"}]}, {"role":
      "user", "content": [{"type": "tool_result", "content": "Here are some key notes
      about the LangChain website based on the content:\n\n1. LangChain offers a suite
      of products for building, running, and managing LLM applications:\n   - LangChain:
      A framework for building with LLMs\n   - LangGraph: A framework for building
      controllable agentic workflows\n   - LangSmith: A platform for debugging, testing,
      deploying and monitoring LLM apps\n\n2. The website highlights that LangChain
      products can be used independently or together.\n\n3. Key stats mentioned:\n   -
      15M+ monthly downloads\n   - 100K+ apps powered\n   - 75K+ GitHub stars\n   -
      3K+ contributors\n\n4. The site emphasizes LangChain''s large developer community
      (1M+ developers).\n\n5. There are testimonials from companies like Retool, Rakuten,
      and Elastic about using LangChain products.\n\n6. The website offers resources
      like documentation, GitHub links, blog, and community pages.\n\n7. There''s
      a newsletter signup form in the footer.\n\n8. The site promotes both free signup
      and enterprise demos.\n\n9. Use cases across industries like financial services,
      fintech, and technology are highlighted.\n\n10. The website design uses a lot
      of green coloring, matching LangChain''s branding.", "tool_use_id": "toolu_018RqnUb3x65zWF9PSDzA2Ns",
      "is_error": false}, {"type": "text", "text": "I am thinking of calling the info
      tool with the info below. Is this good? Give your reasoning as well. You can
      encourage the Assistant to look at specific URLs if that seems relevant, or
      do more searches.\nIf you don''t think it is good, you should be very specific
      about what could be improved.\n\n{\n  \"founder\": \"Harrison Chase\",\n  \"websiteUrl\":
      \"https://www.langchain.com/\",\n  \"products_sold\": [\n    \"LangChain\",\n    \"LangGraph\",\n    \"LangSmith\"\n  ]\n}"}]}],
      "model": "claude-3-5-sonnet-20240620", "tool_choice": {"type": "tool", "name":
      "InfoIsSatisfactory"}, "tools": [{"name": "InfoIsSatisfactory", "description":
      "Validate whether the current extracted info is satisfactory and complete.",
      "input_schema": {"properties": {"reason": {"description": "First, provide reasoning
      for why this is either good or bad as a final result. Must include at least
      3 reasons.", "items": {"type": "string"}, "type": "array"}, "is_satisfactory":
      {"description": "After providing your reasoning, provide a value indicating
      whether the result is satisfactory. If not, you will continue researching.",
      "type": "boolean"}}, "required": ["reason", "is_satisfactory"], "type": "object"}}]}'
    headers: {}
    method: POST
    uri: https://api.anthropic.com/v1/messages
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAAwAAAP//ZJNhbxQ5DIb/ipUvB1LabodeEfO1Uq/oeujUFgRCaOVNPDs+MvEQe7pa
        Vf3vKDNdKNy3ySR+/Np+/eA4utYNul2vTu8+dvjx+vbqP/3nrgnXpxO9+jrtnXe2H6m+IlXckvOu
        SKo/UJXVMJvzbpBIybUuJJwiHb06+vNIJWeyo2bVnK3Om5XzLkg2yubazw8HqImk9aSVOkup52m9
        Ov27ubm5uvxwtjt/d/etuWxefxr+fbd13mUcatzb3MlbvUVj7TCYlCqU8ziZax9cIVTJrv3s7noC
        zp2UAY0lw1jkniNFCHJPRQFTAusJCn2buFCEjilFhRedTDlS8bCjjbLR+5I8YI6VEKdgulZJ8SWg
        go4UuGOKwHmGSeEtZ0wzldSOnZ+FPDH/UKhVeLjCUlglw0WPSh5YIUgpFCztgSNlW7AbVIogC1wJ
        S+ihkE7J1MOIxThMCUvaQ1dkmF9dStmQzoIvypRDXxlA2QqTHvQ8lQbvb67hRW82antystvtjhPm
        beiR83GQ4eRlFYYhTAWNZuSAFnrSpdiu48CY4Brz9qIG/eBajwa72qFQcKR4yPtLCyGxGnAOaYqk
        Pyl+/vyr4Ngvja/H24Gt97DrOfSApc42EsVZyFBTH9BVFtV5bvbzZZBhxLyvZUiJnLdgsrRzkfZD
        85NHq9Qr2dF9tYD1VAgG3vYGG4IiMkAnBXiodqKBsh1G///SfLXIrJ3KPc3tqAEseTGMyvD7UJce
        H9IFycpxLgYzYIxcgzEdch27L96xrvX5MrQdJqXHxy/eqcm4PmzE84WbL7RaNAdybZ5S8m6ad7x9
        WJZpbfKVsrr27Oz8tXcy2fOfTfPm8fE7AAAA//8DACQdP65EBAAA
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c4af84d7c57251c-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 17 Sep 2024 17:55:59 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Robots-Tag:
      - none
      anthropic-ratelimit-requests-limit:
      - '4000'
      anthropic-ratelimit-requests-remaining:
      - '3999'
      anthropic-ratelimit-requests-reset:
      - '2024-09-17T17:56:54Z'
      anthropic-ratelimit-tokens-limit:
      - '400000'
      anthropic-ratelimit-tokens-remaining:
      - '400000'
      anthropic-ratelimit-tokens-reset:
      - '2024-09-17T17:55:59Z'
      request-id:
      - req_01YNF4M4GMfi6ycDLHzK62ey
      via:
      - 1.1 google
      x-cloud-trace-context:
      - 16bf9f43b729a79b26fb35cc24c5dc38
    status:
      code: 200
      message: OK
- request:
    body: '{"max_tokens": 1024, "messages": [{"role": "user", "content": "You are
      doing web research on behalf of a user. You are trying to figure out this information:\n\n<info>\n{\n  \"type\":
      \"object\",\n  \"properties\": {\n    \"providers\": {\n      \"type\": \"array\",\n      \"items\":
      {\n        \"type\": \"object\",\n        \"properties\": {\n          \"name\":
      {\n            \"type\": \"string\",\n            \"description\": \"Company
      name\"\n          },\n          \"technology_summary\": {\n            \"type\":
      \"string\",\n            \"description\": \"Brief summary of their chip technology
      for LLM training\"\n          },\n          \"current_market_share\": {\n            \"type\":
      \"string\",\n            \"description\": \"Estimated current market share percentage
      or position\"\n          },\n          \"future_outlook\": {\n            \"type\":
      \"string\",\n            \"description\": \"Brief paragraph on future prospects
      and developments\"\n          }\n        },\n        \"required\": [\n          \"name\",\n          \"technology_summary\",\n          \"current_market_share\",\n          \"future_outlook\"\n        ]\n      },\n      \"description\":
      \"List of top chip providers for LLM Training\"\n    },\n    \"overall_market_trends\":
      {\n      \"type\": \"string\",\n      \"description\": \"Brief paragraph on
      general trends in the LLM chip market\"\n    }\n  },\n  \"required\": [\n    \"providers\",\n    \"overall_market_trends\"\n  ]\n}\n</info>\n\nYou
      have access to the following tools:\n\n- `Search`: call a search tool and get
      back some results\n- `ScrapeWebsite`: scrape a website and get relevant notes
      about the given request. This will update the notes above.\n- `Info`: call this
      when you are done and have gathered all the relevant info\n\nHere is the information
      you have about the topic you are researching:\n\nTopic: Top 5 chip providers
      for LLM training"}], "model": "claude-3-5-sonnet-20240620", "tool_choice": {"type":
      "any"}, "tools": [{"name": "scrape_website", "description": "Scrape and summarize
      content from a given URL.", "input_schema": {"type": "object", "properties":
      {"url": {"type": "string"}}, "required": ["url"]}}, {"name": "search", "description":
      "Search for general results. This function performs a search using the Tavily
      search engine, which is designed\nto provide comprehensive, accurate, and trusted
      results. It''s particularly useful\nfor answering questions about current events.",
      "input_schema": {"type": "object", "properties": {"query": {"type": "string"}},
      "required": ["query"]}}, {"name": "Info", "description": "Call this when you
      have gathered all the relevant info", "input_schema": {"type": "object", "properties":
      {"providers": {"type": "array", "items": {"type": "object", "properties": {"name":
      {"type": "string", "description": "Company name"}, "technology_summary": {"type":
      "string", "description": "Brief summary of their chip technology for LLM training"},
      "current_market_share": {"type": "string", "description": "Estimated current
      market share percentage or position"}, "future_outlook": {"type": "string",
      "description": "Brief paragraph on future prospects and developments"}}, "required":
      ["name", "technology_summary", "current_market_share", "future_outlook"]}, "description":
      "List of top chip providers for LLM Training"}, "overall_market_trends": {"type":
      "string", "description": "Brief paragraph on general trends in the LLM chip
      market"}}, "required": ["providers", "overall_market_trends"]}}]}'
    headers: {}
    method: POST
    uri: https://api.anthropic.com/v1/messages
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA1SQUUvDQBCE/8qxzxdI2qa296ZYEKwgtFhRSjiSbRKb7Ka3d4UQ8t8lFUEfZ2Zn
        +NgB6gIMtFJmcfJ6eHi+D+WFvpLdZjX/eHeLu9UaNPi+w+kKRWyJoMFxMxlWpBZvyYOGlgtswEDe
        2FBgNI/SSJgIfTSLZ4t4OYtBQ87kkTyYz+F31DM3WZBp9YYy6ZDFyaE89ZsWffu2k/062V6f0sfQ
        gQay7dQTtC6vphZ1wYMZ4BLQ9WBgz51KVV7VneocX+sCnagTO7XdvijvbE01laq17oxeSWUdKkuF
        8phXxA2XPYzjUYN47jKHVpj+Y94CwUtAyhEMhabREG6fMcMPTub5jCRgkiRZauDg/5ppPI7fAAAA
        //8DAJ9/8BZ5AQAA
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c4b1ff70a182368-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 17 Sep 2024 18:23:01 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Robots-Tag:
      - none
      anthropic-ratelimit-requests-limit:
      - '4000'
      anthropic-ratelimit-requests-remaining:
      - '3999'
      anthropic-ratelimit-requests-reset:
      - '2024-09-17T18:23:54Z'
      anthropic-ratelimit-tokens-limit:
      - '400000'
      anthropic-ratelimit-tokens-remaining:
      - '400000'
      anthropic-ratelimit-tokens-reset:
      - '2024-09-17T18:23:01Z'
      request-id:
      - req_01CMZwM2zGhoHhfBYEQ6ThJM
      via:
      - 1.1 google
      x-cloud-trace-context:
      - cedecb2fdef29557856f15dda0f5c1f6
    status:
      code: 200
      message: OK
- request:
    body: '{"max_tokens": 1024, "messages": [{"role": "user", "content": "You are
      doing web research on behalf of a user. You are trying to figure out this information:\n\n<info>\n{\n  \"type\":
      \"object\",\n  \"properties\": {\n    \"providers\": {\n      \"type\": \"array\",\n      \"items\":
      {\n        \"type\": \"object\",\n        \"properties\": {\n          \"name\":
      {\n            \"type\": \"string\",\n            \"description\": \"Company
      name\"\n          },\n          \"technology_summary\": {\n            \"type\":
      \"string\",\n            \"description\": \"Brief summary of their chip technology
      for LLM training\"\n          },\n          \"current_market_share\": {\n            \"type\":
      \"string\",\n            \"description\": \"Estimated current market share percentage
      or position\"\n          },\n          \"future_outlook\": {\n            \"type\":
      \"string\",\n            \"description\": \"Brief paragraph on future prospects
      and developments\"\n          }\n        },\n        \"required\": [\n          \"name\",\n          \"technology_summary\",\n          \"current_market_share\",\n          \"future_outlook\"\n        ]\n      },\n      \"description\":
      \"List of top chip providers for LLM Training\"\n    },\n    \"overall_market_trends\":
      {\n      \"type\": \"string\",\n      \"description\": \"Brief paragraph on
      general trends in the LLM chip market\"\n    }\n  },\n  \"required\": [\n    \"providers\",\n    \"overall_market_trends\"\n  ]\n}\n</info>\n\nYou
      have access to the following tools:\n\n- `Search`: call a search tool and get
      back some results\n- `ScrapeWebsite`: scrape a website and get relevant notes
      about the given request. This will update the notes above.\n- `Info`: call this
      when you are done and have gathered all the relevant info\n\nHere is the information
      you have about the topic you are researching:\n\nTopic: Top 5 chip providers
      for LLM training"}, {"role": "assistant", "content": [{"type": "tool_use", "name":
      "search", "input": {"query": "Top 5 chip providers for LLM training market share
      and technology"}, "id": "toolu_01WgfyEmetmVSsT91LvH5Dup"}]}, {"role": "user",
      "content": [{"type": "tool_result", "content": "[{\"url\": \"https://www.cbinsights.com/company/langchain\",
      \"content\": \"It was founded in 2023 and is based in San Francisco, California.
      LangChain specializes in the development of large language model (LLM) applications,
      providing a suite of products that support developers throughout the application
      lifecycle. Use the CB Insights Platform to explore LangChain''s full profile.\"},
      {\"url\": \"https://en.wikipedia.org/wiki/LangChain\", \"content\": \"In October
      2023 LangChain introduced LangServe, a deployment tool designed to facilitate
      the transition from LCEL (LangChain Expression Language) prototypes to production-ready
      applications.[5]\\nIntegrations[edit]\\nAs of March 2023, LangChain included
      integrations with systems including Amazon, Google, and Microsoft Azure cloud
      storage; API wrappers for news, movie information, and weather; Bash for summarization,
      syntax and semantics checking, and execution of shell scripts; multiple web
      scraping subsystems and templates; few-shot learning prompt generation support;
      finding and summarizing \\\"todo\\\" tasks in code; Google Drive documents,
      spreadsheets, and presentations summarization, extraction, and creation; Google
      Search and Microsoft Bing web search; OpenAI, Anthropic, and Hugging Face language
      models; iFixit repair guides and wikis search and summarization; MapReduce for
      question answering, combining documents, and question generation; N-gram overlap
      scoring; PyPDF, pdfminer, fitz, and pymupdf for PDF file text extraction and
      manipulation; Python and JavaScript code generation, analysis, and debugging;
      Milvus vector database[6] to store and retrieve vector embeddings; Weaviate
      vector database[7] to cache embedding and data objects; Redis cache database
      storage; Python RequestsWrapper and other methods for API requests; SQL and
      NoSQL databases including JSON support; Streamlit, including for logging; text
      mapping for k-nearest neighbors search; time zone conversion and calendar operations;
      tracing and recording stack symbols in threaded and asynchronous subprocess
      runs; and the Wolfram Alpha website and SDK.[8] As a language model integration
      framework, LangChain''s use-cases largely overlap with those of language models
      in general, including document analysis and summarization, chatbots, and code
      analysis.[2]\\nHistory[edit]\\nLangChain was launched in October 2022 as an
      open source project by Harrison Chase, while working at machine learning startup
      Robust Intelligence. In April 2023, LangChain had incorporated and the new startup
      raised over $20 million in funding at a valuation of at least $200 million from
      venture firm Sequoia Capital, a week after announcing a $10 million seed investment
      from Benchmark.[3][4]\\n The project quickly garnered popularity, with improvements
      from hundreds of contributors on GitHub, trending discussions on Twitter, lively
      activity on the project''s Discord server, many YouTube tutorials, and meetups
      in San Francisco and London. As of April 2023, it can read from more than 50
      document types and data sources.[9]\\nReferences[edit]\\nExternal links[edit]\"},
      {\"url\": \"https://www.forbes.com/companies/langchain/\", \"content\": \"LangChain.
      Founded in late 2022, LangChain was an instant hit in Silicon Valley for its
      software that enables anyone to build apps powered by large language models
      like GPT-4 in as little as two ...\"}, {\"url\": \"https://tedai-sanfrancisco.ted.com/speakers-1/harrison-chase-/co-founder-and-ceo,-langchain/\",
      \"content\": \"Harrison Chase, a Harvard graduate in statistics and computer
      science, co-founded LangChain to streamline the development of Language Model
      applications with open-source Python/Typescript packages. Chase''s experience
      includes heading the Machine Learning team at Robust Intelligence, focusing
      on the testing and validation of machine learning models, and leading the entity
      linking team at Kensho ...\"}, {\"url\": \"https://www.crunchbase.com/organization/langchain\",
      \"content\": \"Summary. LangChain may be growing as evidenced by securing $25
      million in funding, which is a significant financial milestone for the company.
      Additionally, the launch of a new platform to facilitate the full lifecycle
      of Large Language Model applications suggests expansion in their product offerings.
      The integration of Gemini Pro API, enabling ...\"}, {\"url\": \"https://craft.co/langchain\",
      \"content\": \"LangChain has 5 employees at their 1 location and $35 m in total
      funding,. See insights on LangChain including office locations, competitors,
      revenue, financials, executives, subsidiaries and more at Craft. ... Harrison
      Chase, Co-Founder and Chief Executive Officer. Linkedin;\"}, {\"url\": \"https://www.langchain.com/about\",
      \"content\": \"Ready to start shipping\\nreliable GenAI apps faster?\\nLangChain
      and LangSmith are critical parts of the reference\\narchitecture to get you
      from prototype to production. Announcing the General Availability of LangSmith\\nToday,
      we\\u2019re thrilled to announce the general availability of LangSmith \\u2014
      our solution for LLM application development, monitoring, and testing.\\n We
      build products that enable developers to go from an idea to working code in
      an afternoon and in the hands of users in days or weeks. The biggest developer
      community in \\u00a0GenAI\\nLearn alongside the 100K+ practitioners\\nwho are
      pushing the industry forward.\\n And we built LangSmith to support all stages
      of the AI engineering lifecycle, to get applications into production faster.\\n\"},
      {\"url\": \"https://www.bluelabellabs.com/blog/what-is-langchain/\", \"content\":
      \"Similar setups to AutoTweeter would allow businesses to parse past and present
      customer queries to understand drop-off points and build the appropriate flows
      to solve customer problems on the spot, instead of spitting them into a queue
      to wait for a human and plot how they\\u2019re going to steal from your business.\\n
      Rather than creating a bot to tweet in your tone on your behalf about your love
      for processed snacks, hockey, and political happenings, you could train such
      a system on your brand voice, knowledge bases, and other resources to create
      a highly valuable feature for your digital touchpoints.\\n Here, you essentially
      create a curated ML system that\\u2019s tailored to your brand with specific
      issues and requests from your customers \\u2013 with a little bit of dev magic,
      you can quickly build a solution that integrates features like payment systems,
      tech support, ordering, account management, and so much more.\\n Creating a
      bot for social media that can post just like you\\nSocial media already has
      enough bots engaging with people as if they\\u2019re real people, but for the
      sake of making a functional example, our co-founder Bobby created a bot called
      AutoTweeter for the social media platform we all still call Twitter.\\n How
      does LangChain work?\\nLangchain essentially serves as a middleman between other
      LLMs, data sources, and API-accessible functions \\u2013 more precisely, it\\u2019s
      a comprehensive open-source library of components that allow users to build
      better (i.e., more custom) solutions by interconnecting different resources.\\n\"},
      {\"url\": \"https://www.latent.space/p/langchain\", \"content\": \"And so you
      can do a lot of things like reflection and kind of like making sure that you''re
      on the right track and really put more safeguards and thinking about these agents
      as opposed to relying on like chas and interface, like the bot we have that''s
      on GitHub answering questions on the issues, I think probably gives better answers
      than the bots that we have that are on chat on the website. [00:50:32]\\nHarrison:
      And then between kind of like allowing for, I think one helpful thing that we
      did in terms of like distinguishing between basically the base interfaces and
      then more complex stuff is part of the separation around the docs is there''s
      like the components piece, which has the model IO, the retrieval, the agents,
      the callbacks, things like that. And then like, I know a lot of people are doing
      this, but like a personal assistant for like managing like email calendar, basic
      stuff, which I think is, I think that''s like a fantastic application for these
      like agent like things, because if you think about personal assistants today,
      you usually interact, I don''t have one, but I''m told you interact with them
      over email. Show Notes\\nLangChain\\nLangChain\\u2019s Berkshire Hathaway Homepage\\nAbstractions
      tweet\\nLangSmith\\nLangSmith Cookbooks repo\\nLangChain Retrieval blog\\nEvaluating
      CSV Question/Answering blog and YouTube\\nMultiOn Partner blog\\nHarvard Sports
      Analytics Collective\\nEvaluating RAG Webinar\\nawesome-langchain:\\nLLM Math
      Chain\\nSelf-Ask\\nLangChain Hub UI\\n\\u201cLangChain is Pointless\\u201d\\nHarrison\\u2019s
      links\\nsports - estimating player compatibility in the NBA\\nearly interest
      in prompt injections\\nGitHub\\nTwitter\\nTimestamps\\n[00:00:00] Introduction\\n[00:00:48]
      Harrison''s background and how sports led him into ML\\n[00:04:54] Memory (for
      long running chats, whether a simple Buffer, Knowledge Graph, Summary, or Vector
      Store)\\nUse-Cases: compositions of Components\\nChains: combining a PromptTemplate,
      LLM Model and optional OutputParser\\nwith Router, Sequential, and Transform
      Chains for advanced usecases\\nsavable, sharable schemas that can be loaded
      from LangChainHub\\nAgents: a chain that has access to a suite of tools, of
      nondeterministic8 length because the LLM is used as a reasoning engine to determine
      which actions to take and in which order.\"}, {\"url\": \"https://www.langchain.com/\",
      \"content\": \"Join Discord\\nContribute on GitHub\\n5.3M\\nMonthly Downloads\\n67k\\nGithub
      Stars\\n1800+\\nContributors\\n34k+\\nApps Powered\\nReady to build?\\nGo To
      Docs\\nGo To Docs\\nGo To Docs\\nContact Sales\\nContact Sales\\nContact Sales\\nProducts
      and use-cases\\nLangChain\\nLangSmith\\nRetrieval\\nAgents\\nInspiration\\nCode\\nGitHub\\nLangChain
      Hub\\nPython Docs\\nJS/TS Docs\\nSocial\\nTwitter\\nDiscord\\nBlog\\nLinkedIn\\nYouTube\\nTerms
      of Service\\nSign up for our newsletter\\nProducts and use-cases\\nLangChain\\nLangSmith\\nRetrieval\\nAgents\\nInspiration\\nCode\\nGitHub\\nLangChain
      Hub\\nPython Docs\\nJS/TS Docs\\nSocial\\nTwitter\\nDiscord\\nBlog\\nLinkedIn\\nYouTube\\nTerms
      of Service\\nSign up for our newsletter\\nProducts and use-cases\\nLangChain\\nLangSmith\\nRetrieval\\nAgents\\nInspiration\\nCode\\nGitHub\\nLangChain
      Hub\\nPython Docs\\nJS/TS Docs\\nSocial\\nTwitter\\nDiscord\\nBlog\\nLinkedIn\\nYouTube\\nTerms
      of Service\\nSign up for our newsletter Chatbots\\nQ&A Over Docs\\nSummarization\\nCopilots\\nWorkflow
      Automation\\nDocument Analysis\\nCustom Search\\nHarness the power\\u2013and
      wrangle the complexity\\u2013 of LLMs\\nGet from proof-of-concept to production
      on one platform\\nRely on a set of tools built especially for non-deterministic
      models\\nLeverage new cognitive architectures and battle-tested orchestration\\nThanks
      to our contributors and partners for making LangChain awesome\\nWhen you build
      with LangChain, you build alongside a community of developers advancing the
      frontier of what\\u2019s possible with LLMs.\\n \\ud83e\\udd9c\\ud83d\\udd17
      LangChain\\nLangSmith\\nLangServe\\nAgents\\nRetrieval\\nEvaluation\\nBlog\\nDocs\\n\\ud83e\\udd9c\\ud83d\\udd17
      LangChain\\n\\ud83e\\udd9c\\ud83d\\udd17 LangChain\\nLangChain Expression Language
      makes it easy to create custom chains.\\n Go To Docs\\nGo To Docs\\nGo To Docs\\nContact
      Sales\\nglobal corporations, STARTUPS, and TINKERERS build with LangChain\\nglobal
      corporations, STARTUPS, and TINKERERS build with LangChain\\nglobal corporations,
      STARTUPS, and TINKERERS build with LangChain\\nA complete set of powerful building
      blocks\\nA complete set of powerful building blocks\\n Try it out here\\nGet
      your LLM application from prototype to production\\nGet your LLM application
      from prototype to production\\nBuild context-aware, reasoning applications with
      LangChain\\u2019s flexible abstractions and AI-first toolkit.\\n\"}]", "tool_use_id":
      "toolu_01WgfyEmetmVSsT91LvH5Dup", "is_error": false}]}], "model": "claude-3-5-sonnet-20240620",
      "tool_choice": {"type": "any"}, "tools": [{"name": "scrape_website", "description":
      "Scrape and summarize content from a given URL.", "input_schema": {"type": "object",
      "properties": {"url": {"type": "string"}}, "required": ["url"]}}, {"name": "search",
      "description": "Search for general results. This function performs a search
      using the Tavily search engine, which is designed\nto provide comprehensive,
      accurate, and trusted results. It''s particularly useful\nfor answering questions
      about current events.", "input_schema": {"type": "object", "properties": {"query":
      {"type": "string"}}, "required": ["query"]}}, {"name": "Info", "description":
      "Call this when you have gathered all the relevant info", "input_schema": {"type":
      "object", "properties": {"providers": {"type": "array", "items": {"type": "object",
      "properties": {"name": {"type": "string", "description": "Company name"}, "technology_summary":
      {"type": "string", "description": "Brief summary of their chip technology for
      LLM training"}, "current_market_share": {"type": "string", "description": "Estimated
      current market share percentage or position"}, "future_outlook": {"type": "string",
      "description": "Brief paragraph on future prospects and developments"}}, "required":
      ["name", "technology_summary", "current_market_share", "future_outlook"]}, "description":
      "List of top chip providers for LLM Training"}, "overall_market_trends": {"type":
      "string", "description": "Brief paragraph on general trends in the LLM chip
      market"}}, "required": ["providers", "overall_market_trends"]}}]}'
    headers: {}
    method: POST
    uri: https://api.anthropic.com/v1/messages
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA4xWYW8bNwz9K8QBQzfACdyk61p/C5oh9doEHpq0A4bBoHX0HVGdeBV1dt0g/32g
        7uzY6ZL1m62TqMf3yEfdFlwWk6LRaj5+fvXqj9fviP969W39iVz17uvl9WnzZzEq0qYl20WqWFEx
        KqJ4W0BV1oQhFaOikZJ8MSmcx66ko9OjX49UQqB0dDI+eTF+eTIuRoWTkCikYvL37TZoEvHzTi1q
        hmL/u/n4+afrZjZr+CY5udSGPp69/LL8/bQYFQEbOzcNS7Ezoe1SMbkt2igrLilqDj5suvo4PZ+e
        WQbk6iBeqs1cu6bBuCkmxVuu6qOW4lJig8ERXMxuFKRN3PA3KmEpEc6mgKGEBl3NgcATxsChGgEH
        57uSQwWpJjh7Ph7njW/th1JkUsu3i5FCmjcYP1Oaa43RYJ1LwwFDGgFp4gYTlSArivB6/BPIso84
        BVdzC/3RYlQsu9RFmkuXvMjnXXLACvS1JWdBkkCDHBJyAE5qcI0Si9OKcmIJI1h0CZboSC2HSKiW
        hJOmpZR3HMN1TXkBwwZMMg4dqQXnEGSFiWDNqYZAa8Doak7kDJtmBlqMKfSX5jMqnktebjKgPpsd
        mOPibrQT6+zy/DGlLqenO1oBnSNPEZNEHRnOBZsk8MbUMwRZxkG8tcTPXrB8Qo2LKOssqRGjib0H
        BG3Qe2glGsyejNgTvKup7wQ5uzw3NSrs8aSILp/OZFn2b86vzg4Yy3D3kzvk/oG0g1y0z2ROAjjk
        mnFWVxVsCI0ZE4Jd5zH6zXZHiQnBUUgU8931wxawq7tkUZSqhkLSA42mIZF/tJ9wgQHhPS70GVxg
        V/JJ7p89vfKlXTsAnZkdwEdyrmbJsj2u0ocsiElEmnDhWWsqoY2kZLiH/P6/a3IGxiyHlXVfqKAm
        XHHP0fZ8SSvy0hoBGTJyk4t5QU4agkYi3bfMinrZtHOOVK2DTaE9OgDdl477ms/xeliWMshySZFD
        pbC20lsQuNg5Rp9LeFtM+3IfKHIhUnl6TJJrCioRZlEMmQW6CYbt5+vZjf4CJSlXgUrQlhwv2aH3
        m3zvQ8P7kUb6wFXIQUIyLp2XrjxaoFJpvCrFFTvSvs+CJFhzSX4DuEL2uPA7ESsKFNE/IWKf9KE3
        DZJlermxcdA3yvXsBu65OYZPNXuCNnKD0VTvDN/gl7IeYO+hTTVFeqbQio2urS6GPQKWNi0kgETw
        7Chkioc0etSHYkVsayfxUb1ydXquck1/r9r0ULUnBhRg23p2aOiekOyKXU3QetxQ7H2q6t0QIjmp
        Ql+xP9xcu/z2fRBTJk5Cxmt5DGOEVwTT2c2BIx4aYI0PeTd4qbbBaRWby5UDDH/y1DZC9nLfn3ea
        ohzOOlhGaQ4dJXNhrvfPqLChjN5vWUuRQqnWVjXB+/eX+3RszToyBZcJxJbLAW9flPb2UToctcMQ
        H+TxGyjz2yCR3o+Ni4MKHvqH1kAhRQypN9X9HGwzVNx/y+PhP6zuIWd7Lp1FyEWPkHOGJGuMpfbG
        Z+1ZbY5ouWTHW4dUh30Tq/huYB4V1mTjVDNT+oTf7I/qY3iTe3D3pMtJoNddkw/PLo59x3aapOmv
        6OvnUJLt/NzahS1iyG83NExNduQneslhiwv2nPj+0ZQfIlFUYYWRpbMPZacpT/HizqpHk7Rz2yvh
        8KGbPyh96azNi0novB8VXX5bT277J+08yWcKWkxenL54OSqkS/uLv41P7u7+BQAA//8DAB7P4A+8
        CwAA
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c4b20015f697ad0-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 17 Sep 2024 18:23:14 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Robots-Tag:
      - none
      anthropic-ratelimit-requests-limit:
      - '4000'
      anthropic-ratelimit-requests-remaining:
      - '3999'
      anthropic-ratelimit-requests-reset:
      - '2024-09-17T18:23:54Z'
      anthropic-ratelimit-tokens-limit:
      - '400000'
      anthropic-ratelimit-tokens-remaining:
      - '400000'
      anthropic-ratelimit-tokens-reset:
      - '2024-09-17T18:23:14Z'
      request-id:
      - req_01Dv6svvdB3VpSpXUSrHhhgK
      via:
      - 1.1 google
      x-cloud-trace-context:
      - d7ef3c2e361965193d12822c4d4fbc0a
    status:
      code: 200
      message: OK
- request:
    body: '{"max_tokens": 1024, "messages": [{"role": "user", "content": "You are
      doing web research on behalf of a user. You are trying to figure out this information:\n\n<info>\n{\n  \"type\":
      \"object\",\n  \"properties\": {\n    \"providers\": {\n      \"type\": \"array\",\n      \"items\":
      {\n        \"type\": \"object\",\n        \"properties\": {\n          \"name\":
      {\n            \"type\": \"string\",\n            \"description\": \"Company
      name\"\n          },\n          \"technology_summary\": {\n            \"type\":
      \"string\",\n            \"description\": \"Brief summary of their chip technology
      for LLM training\"\n          },\n          \"current_market_share\": {\n            \"type\":
      \"string\",\n            \"description\": \"Estimated current market share percentage
      or position\"\n          },\n          \"future_outlook\": {\n            \"type\":
      \"string\",\n            \"description\": \"Brief paragraph on future prospects
      and developments\"\n          }\n        },\n        \"required\": [\n          \"name\",\n          \"technology_summary\",\n          \"current_market_share\",\n          \"future_outlook\"\n        ]\n      },\n      \"description\":
      \"List of top chip providers for LLM Training\"\n    },\n    \"overall_market_trends\":
      {\n      \"type\": \"string\",\n      \"description\": \"Brief paragraph on
      general trends in the LLM chip market\"\n    }\n  },\n  \"required\": [\n    \"providers\",\n    \"overall_market_trends\"\n  ]\n}\n</info>\n\nYou
      have access to the following tools:\n\n- `Search`: call a search tool and get
      back some results\n- `ScrapeWebsite`: scrape a website and get relevant notes
      about the given request. This will update the notes above.\n- `Info`: call this
      when you are done and have gathered all the relevant info\n\nHere is the information
      you have about the topic you are researching:\n\nTopic: Top 5 chip providers
      for LLM training"}, {"role": "assistant", "content": [{"type": "tool_use", "name":
      "search", "input": {"query": "Top 5 chip providers for LLM training market share
      and technology"}, "id": "toolu_01WgfyEmetmVSsT91LvH5Dup"}]}, {"role": "user",
      "content": [{"type": "tool_result", "content": "[{\"url\": \"https://www.cbinsights.com/company/langchain\",
      \"content\": \"It was founded in 2023 and is based in San Francisco, California.
      LangChain specializes in the development of large language model (LLM) applications,
      providing a suite of products that support developers throughout the application
      lifecycle. Use the CB Insights Platform to explore LangChain''s full profile.\"},
      {\"url\": \"https://en.wikipedia.org/wiki/LangChain\", \"content\": \"In October
      2023 LangChain introduced LangServe, a deployment tool designed to facilitate
      the transition from LCEL (LangChain Expression Language) prototypes to production-ready
      applications.[5]\\nIntegrations[edit]\\nAs of March 2023, LangChain included
      integrations with systems including Amazon, Google, and Microsoft Azure cloud
      storage; API wrappers for news, movie information, and weather; Bash for summarization,
      syntax and semantics checking, and execution of shell scripts; multiple web
      scraping subsystems and templates; few-shot learning prompt generation support;
      finding and summarizing \\\"todo\\\" tasks in code; Google Drive documents,
      spreadsheets, and presentations summarization, extraction, and creation; Google
      Search and Microsoft Bing web search; OpenAI, Anthropic, and Hugging Face language
      models; iFixit repair guides and wikis search and summarization; MapReduce for
      question answering, combining documents, and question generation; N-gram overlap
      scoring; PyPDF, pdfminer, fitz, and pymupdf for PDF file text extraction and
      manipulation; Python and JavaScript code generation, analysis, and debugging;
      Milvus vector database[6] to store and retrieve vector embeddings; Weaviate
      vector database[7] to cache embedding and data objects; Redis cache database
      storage; Python RequestsWrapper and other methods for API requests; SQL and
      NoSQL databases including JSON support; Streamlit, including for logging; text
      mapping for k-nearest neighbors search; time zone conversion and calendar operations;
      tracing and recording stack symbols in threaded and asynchronous subprocess
      runs; and the Wolfram Alpha website and SDK.[8] As a language model integration
      framework, LangChain''s use-cases largely overlap with those of language models
      in general, including document analysis and summarization, chatbots, and code
      analysis.[2]\\nHistory[edit]\\nLangChain was launched in October 2022 as an
      open source project by Harrison Chase, while working at machine learning startup
      Robust Intelligence. In April 2023, LangChain had incorporated and the new startup
      raised over $20 million in funding at a valuation of at least $200 million from
      venture firm Sequoia Capital, a week after announcing a $10 million seed investment
      from Benchmark.[3][4]\\n The project quickly garnered popularity, with improvements
      from hundreds of contributors on GitHub, trending discussions on Twitter, lively
      activity on the project''s Discord server, many YouTube tutorials, and meetups
      in San Francisco and London. As of April 2023, it can read from more than 50
      document types and data sources.[9]\\nReferences[edit]\\nExternal links[edit]\"},
      {\"url\": \"https://www.forbes.com/companies/langchain/\", \"content\": \"LangChain.
      Founded in late 2022, LangChain was an instant hit in Silicon Valley for its
      software that enables anyone to build apps powered by large language models
      like GPT-4 in as little as two ...\"}, {\"url\": \"https://tedai-sanfrancisco.ted.com/speakers-1/harrison-chase-/co-founder-and-ceo,-langchain/\",
      \"content\": \"Harrison Chase, a Harvard graduate in statistics and computer
      science, co-founded LangChain to streamline the development of Language Model
      applications with open-source Python/Typescript packages. Chase''s experience
      includes heading the Machine Learning team at Robust Intelligence, focusing
      on the testing and validation of machine learning models, and leading the entity
      linking team at Kensho ...\"}, {\"url\": \"https://www.crunchbase.com/organization/langchain\",
      \"content\": \"Summary. LangChain may be growing as evidenced by securing $25
      million in funding, which is a significant financial milestone for the company.
      Additionally, the launch of a new platform to facilitate the full lifecycle
      of Large Language Model applications suggests expansion in their product offerings.
      The integration of Gemini Pro API, enabling ...\"}, {\"url\": \"https://craft.co/langchain\",
      \"content\": \"LangChain has 5 employees at their 1 location and $35 m in total
      funding,. See insights on LangChain including office locations, competitors,
      revenue, financials, executives, subsidiaries and more at Craft. ... Harrison
      Chase, Co-Founder and Chief Executive Officer. Linkedin;\"}, {\"url\": \"https://www.langchain.com/about\",
      \"content\": \"Ready to start shipping\\nreliable GenAI apps faster?\\nLangChain
      and LangSmith are critical parts of the reference\\narchitecture to get you
      from prototype to production. Announcing the General Availability of LangSmith\\nToday,
      we\\u2019re thrilled to announce the general availability of LangSmith \\u2014
      our solution for LLM application development, monitoring, and testing.\\n We
      build products that enable developers to go from an idea to working code in
      an afternoon and in the hands of users in days or weeks. The biggest developer
      community in \\u00a0GenAI\\nLearn alongside the 100K+ practitioners\\nwho are
      pushing the industry forward.\\n And we built LangSmith to support all stages
      of the AI engineering lifecycle, to get applications into production faster.\\n\"},
      {\"url\": \"https://www.bluelabellabs.com/blog/what-is-langchain/\", \"content\":
      \"Similar setups to AutoTweeter would allow businesses to parse past and present
      customer queries to understand drop-off points and build the appropriate flows
      to solve customer problems on the spot, instead of spitting them into a queue
      to wait for a human and plot how they\\u2019re going to steal from your business.\\n
      Rather than creating a bot to tweet in your tone on your behalf about your love
      for processed snacks, hockey, and political happenings, you could train such
      a system on your brand voice, knowledge bases, and other resources to create
      a highly valuable feature for your digital touchpoints.\\n Here, you essentially
      create a curated ML system that\\u2019s tailored to your brand with specific
      issues and requests from your customers \\u2013 with a little bit of dev magic,
      you can quickly build a solution that integrates features like payment systems,
      tech support, ordering, account management, and so much more.\\n Creating a
      bot for social media that can post just like you\\nSocial media already has
      enough bots engaging with people as if they\\u2019re real people, but for the
      sake of making a functional example, our co-founder Bobby created a bot called
      AutoTweeter for the social media platform we all still call Twitter.\\n How
      does LangChain work?\\nLangchain essentially serves as a middleman between other
      LLMs, data sources, and API-accessible functions \\u2013 more precisely, it\\u2019s
      a comprehensive open-source library of components that allow users to build
      better (i.e., more custom) solutions by interconnecting different resources.\\n\"},
      {\"url\": \"https://www.latent.space/p/langchain\", \"content\": \"And so you
      can do a lot of things like reflection and kind of like making sure that you''re
      on the right track and really put more safeguards and thinking about these agents
      as opposed to relying on like chas and interface, like the bot we have that''s
      on GitHub answering questions on the issues, I think probably gives better answers
      than the bots that we have that are on chat on the website. [00:50:32]\\nHarrison:
      And then between kind of like allowing for, I think one helpful thing that we
      did in terms of like distinguishing between basically the base interfaces and
      then more complex stuff is part of the separation around the docs is there''s
      like the components piece, which has the model IO, the retrieval, the agents,
      the callbacks, things like that. And then like, I know a lot of people are doing
      this, but like a personal assistant for like managing like email calendar, basic
      stuff, which I think is, I think that''s like a fantastic application for these
      like agent like things, because if you think about personal assistants today,
      you usually interact, I don''t have one, but I''m told you interact with them
      over email. Show Notes\\nLangChain\\nLangChain\\u2019s Berkshire Hathaway Homepage\\nAbstractions
      tweet\\nLangSmith\\nLangSmith Cookbooks repo\\nLangChain Retrieval blog\\nEvaluating
      CSV Question/Answering blog and YouTube\\nMultiOn Partner blog\\nHarvard Sports
      Analytics Collective\\nEvaluating RAG Webinar\\nawesome-langchain:\\nLLM Math
      Chain\\nSelf-Ask\\nLangChain Hub UI\\n\\u201cLangChain is Pointless\\u201d\\nHarrison\\u2019s
      links\\nsports - estimating player compatibility in the NBA\\nearly interest
      in prompt injections\\nGitHub\\nTwitter\\nTimestamps\\n[00:00:00] Introduction\\n[00:00:48]
      Harrison''s background and how sports led him into ML\\n[00:04:54] Memory (for
      long running chats, whether a simple Buffer, Knowledge Graph, Summary, or Vector
      Store)\\nUse-Cases: compositions of Components\\nChains: combining a PromptTemplate,
      LLM Model and optional OutputParser\\nwith Router, Sequential, and Transform
      Chains for advanced usecases\\nsavable, sharable schemas that can be loaded
      from LangChainHub\\nAgents: a chain that has access to a suite of tools, of
      nondeterministic8 length because the LLM is used as a reasoning engine to determine
      which actions to take and in which order.\"}, {\"url\": \"https://www.langchain.com/\",
      \"content\": \"Join Discord\\nContribute on GitHub\\n5.3M\\nMonthly Downloads\\n67k\\nGithub
      Stars\\n1800+\\nContributors\\n34k+\\nApps Powered\\nReady to build?\\nGo To
      Docs\\nGo To Docs\\nGo To Docs\\nContact Sales\\nContact Sales\\nContact Sales\\nProducts
      and use-cases\\nLangChain\\nLangSmith\\nRetrieval\\nAgents\\nInspiration\\nCode\\nGitHub\\nLangChain
      Hub\\nPython Docs\\nJS/TS Docs\\nSocial\\nTwitter\\nDiscord\\nBlog\\nLinkedIn\\nYouTube\\nTerms
      of Service\\nSign up for our newsletter\\nProducts and use-cases\\nLangChain\\nLangSmith\\nRetrieval\\nAgents\\nInspiration\\nCode\\nGitHub\\nLangChain
      Hub\\nPython Docs\\nJS/TS Docs\\nSocial\\nTwitter\\nDiscord\\nBlog\\nLinkedIn\\nYouTube\\nTerms
      of Service\\nSign up for our newsletter\\nProducts and use-cases\\nLangChain\\nLangSmith\\nRetrieval\\nAgents\\nInspiration\\nCode\\nGitHub\\nLangChain
      Hub\\nPython Docs\\nJS/TS Docs\\nSocial\\nTwitter\\nDiscord\\nBlog\\nLinkedIn\\nYouTube\\nTerms
      of Service\\nSign up for our newsletter Chatbots\\nQ&A Over Docs\\nSummarization\\nCopilots\\nWorkflow
      Automation\\nDocument Analysis\\nCustom Search\\nHarness the power\\u2013and
      wrangle the complexity\\u2013 of LLMs\\nGet from proof-of-concept to production
      on one platform\\nRely on a set of tools built especially for non-deterministic
      models\\nLeverage new cognitive architectures and battle-tested orchestration\\nThanks
      to our contributors and partners for making LangChain awesome\\nWhen you build
      with LangChain, you build alongside a community of developers advancing the
      frontier of what\\u2019s possible with LLMs.\\n \\ud83e\\udd9c\\ud83d\\udd17
      LangChain\\nLangSmith\\nLangServe\\nAgents\\nRetrieval\\nEvaluation\\nBlog\\nDocs\\n\\ud83e\\udd9c\\ud83d\\udd17
      LangChain\\n\\ud83e\\udd9c\\ud83d\\udd17 LangChain\\nLangChain Expression Language
      makes it easy to create custom chains.\\n Go To Docs\\nGo To Docs\\nGo To Docs\\nContact
      Sales\\nglobal corporations, STARTUPS, and TINKERERS build with LangChain\\nglobal
      corporations, STARTUPS, and TINKERERS build with LangChain\\nglobal corporations,
      STARTUPS, and TINKERERS build with LangChain\\nA complete set of powerful building
      blocks\\nA complete set of powerful building blocks\\n Try it out here\\nGet
      your LLM application from prototype to production\\nGet your LLM application
      from prototype to production\\nBuild context-aware, reasoning applications with
      LangChain\\u2019s flexible abstractions and AI-first toolkit.\\n\"}]", "tool_use_id":
      "toolu_01WgfyEmetmVSsT91LvH5Dup", "is_error": false}, {"type": "text", "text":
      "I am thinking of calling the info tool with the info below. Is this good? Give
      your reasoning as well. You can encourage the Assistant to look at specific
      URLs if that seems relevant, or do more searches.\nIf you don''t think it is
      good, you should be very specific about what could be improved.\n\n{\n  \"providers\":
      [\n    {\n      \"name\": \"NVIDIA\",\n      \"technology_summary\": \"High-performance
      GPUs optimized for AI and machine learning, including the A100 and H100 series\",\n      \"current_market_share\":
      \"Dominant, estimated over 90% of the AI chip market\",\n      \"future_outlook\":
      \"NVIDIA is expected to maintain its leadership position, but faces increasing
      competition. The company continues to innovate with new architectures and partnerships
      to solidify its market position.\"\n    },\n    {\n      \"name\": \"AMD\",\n      \"technology_summary\":
      \"MI300 series accelerators, combining CPUs and GPUs for AI workloads\",\n      \"current_market_share\":
      \"Growing, but still a small portion compared to NVIDIA\",\n      \"future_outlook\":
      \"AMD is gaining traction with its CDNA architecture and MI300 series. The company
      is expected to increase its market share in the coming years, particularly in
      the data center and high-performance computing segments.\"\n    },\n    {\n      \"name\":
      \"Intel\",\n      \"technology_summary\": \"Habana Labs'' Gaudi2 AI accelerators
      and upcoming Ponte Vecchio GPUs\",\n      \"current_market_share\": \"Small
      but established presence in the AI chip market\",\n      \"future_outlook\":
      \"Intel is investing heavily in AI chip development and aims to become more
      competitive. The success of its Habana Labs acquisition and future GPU offerings
      will be crucial for gaining market share.\"\n    },\n    {\n      \"name\":
      \"Google\",\n      \"technology_summary\": \"Tensor Processing Units (TPUs)
      designed specifically for machine learning workloads\",\n      \"current_market_share\":
      \"Significant in cloud-based AI services, but not widely available in the general
      market\",\n      \"future_outlook\": \"Google continues to develop and improve
      its TPU technology. While primarily used in its own cloud services, there''s
      potential for wider adoption or licensing in the future.\"\n    },\n    {\n      \"name\":
      \"Graphcore\",\n      \"technology_summary\": \"Intelligence Processing Units
      (IPUs) designed for AI and machine learning applications\",\n      \"current_market_share\":
      \"Niche player with growing recognition in the AI chip market\",\n      \"future_outlook\":
      \"Graphcore is gaining attention for its innovative IPU architecture. The company
      has potential for growth, especially in specialized AI applications, but faces
      strong competition from established players.\"\n    }\n  ],\n  \"overall_market_trends\":
      \"The LLM chip market is experiencing rapid growth and intense competition.
      NVIDIA currently dominates with its GPU technology, but new entrants and established
      tech giants are investing heavily in specialized AI accelerators. There''s a
      trend towards more energy-efficient and scalable solutions, as well as chips
      designed specifically for AI workloads. Cloud providers are also developing
      their own custom chips. The market is expected to continue expanding as demand
      for AI and machine learning capabilities increases across various industries.\"\n}"}]}],
      "model": "claude-3-5-sonnet-20240620", "tool_choice": {"type": "tool", "name":
      "InfoIsSatisfactory"}, "tools": [{"name": "InfoIsSatisfactory", "description":
      "Validate whether the current extracted info is satisfactory and complete.",
      "input_schema": {"properties": {"reason": {"description": "First, provide reasoning
      for why this is either good or bad as a final result. Must include at least
      3 reasons.", "items": {"type": "string"}, "type": "array"}, "is_satisfactory":
      {"description": "After providing your reasoning, provide a value indicating
      whether the result is satisfactory. If not, you will continue researching.",
      "type": "boolean"}}, "required": ["reason", "is_satisfactory"], "type": "object"}}]}'
    headers: {}
    method: POST
    uri: https://api.anthropic.com/v1/messages
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA0xT0U4bMRD8lZVf+uKgJAQo95aWigYRXmjaSghFlr13Z+LzXr17gRDl3yvfEZTH
        s9czszNze+WdKlTD1Xo8mfP15Ft8fFull8f5XWvv3r4/PEyVVrJrMU8hs6lQaZUo5APD7FlMFKVV
        Qw6DKpQNpnM4Oh9djJhiRBlNx9PZ+HI6VlpZioJRVPG0P4IKUVh3nFF7Kfm7W48ny6vGzt5WL9fv
        t245+bta/bHVz1ZpFU2T3y1iSQt+NOK5NFYo7TJCbDtRxV4lNExRFU/qV43gY0mpMeIpQpto6x06
        sLTFxCA1glALF2Br3x6vE0NJCe7vlyDJ+OhjBYYh4b8OWdBp8NGGzuXzh9+Lm8Vcw3x5o2ERBYOG
        W6IqoAYTHdwm09aWEp4prX4YW3+SfGHAKGn3AYaDmkziEzooPQbHBeSNNQjaOlKgagfcNY1JOw22
        SwmjQGPSBgW4NumDtOykSwjUSSDaaKj8tt8BLDVtwhoj+y1C9mDr8RWozNw+QUvse6N87NUM0Fl6
        djLPmxCOhJIwOgZGe+otg4GKyB11foB/qmUxgqcqnU8DAh9Hs/F9HgORhtpXdfBVLXmLDe6gJR+F
        gTtb52SGEL4wOGp8NNFiH1HuQZ9106L0ew329Kln8SD0alLeoUXrTfDv6GC+AGMtBkxGKPGZetbK
        85pPy1aUJjAeDs9asVC7PjbutND9BefORIuqiF0IWnX9P1Tsh7KuhTYYWRWzr7NLraiT08PJ1fnh
        8B8AAP//AwBvxqRDpAMAAA==
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8c4b20560e191722-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Tue, 17 Sep 2024 18:23:19 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Robots-Tag:
      - none
      anthropic-ratelimit-requests-limit:
      - '4000'
      anthropic-ratelimit-requests-remaining:
      - '3999'
      anthropic-ratelimit-requests-reset:
      - '2024-09-17T18:23:54Z'
      anthropic-ratelimit-tokens-limit:
      - '400000'
      anthropic-ratelimit-tokens-remaining:
      - '400000'
      anthropic-ratelimit-tokens-reset:
      - '2024-09-17T18:23:19Z'
      request-id:
      - req_016Pta6sCCcxe74qtjr2Krgh
      via:
      - 1.1 google
      x-cloud-trace-context:
      - 2dbbddef4ed8fa1800e744fabf982670
    status:
      code: 200
      message: OK
version: 1
